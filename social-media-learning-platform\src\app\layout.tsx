import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { GameProvider } from '@/contexts/GameContext'
import { AuthProvider } from '@/contexts/AuthContext'
import { ConditionalNavigation } from '@/components/Navigation/ConditionalNavigation'
import { AudioControlButton } from '@/components/Settings/AudioSettings'
import { CelebrationEffects } from '@/components/UI/CelebrationEffects'
import { ErrorBoundary } from '@/components/ErrorBoundary/ErrorBoundary'
import { NetworkStatus } from '@/components/UI/LoadingSpinner'


const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Social Media Mastery Quest',
  description: 'An interactive, gamified learning platform for mastering social media marketing',
  keywords: 'social media, marketing, learning, gamification, Instagram, Facebook, Google AdWords',
  authors: [{ name: 'Social Media Learning Platform' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50`}>
        <ErrorBoundary>
          <AuthProvider>
            <GameProvider>
              <div className="flex flex-col min-h-screen">
                <ConditionalNavigation />
                <main className="flex-1">
                  <ErrorBoundary>
                    {children}
                  </ErrorBoundary>
                </main>
              <footer className="bg-gray-900 text-white py-8 mt-auto">
                <div className="container mx-auto px-4 text-center">
                  <p>&copy; 2025 Social Media Mastery Quest. All rights reserved.</p>
                  <p className="text-gray-400 mt-2">Learn. Play. Master Social Media Marketing.</p>
                </div>
              </footer>
              </div>
              <AudioControlButton />
              <CelebrationEffects />
              <NetworkStatus />
            </GameProvider>
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
