'use client'

import { motion } from 'framer-motion'
import { <PERSON>, BookOpen, CheckCircle, Lock, Play } from 'lucide-react'
import Link from 'next/link'
import { useGame } from '@/contexts/GameContext'

interface ModuleCardProps {
  module: {
    id: string
    title: string
    description: string
    level: string
    chapters: number
    estimatedTime: string
    color: string
    icon: React.ComponentType<any>
    topics: string[]
  }
}

export function ModuleCard({ module }: ModuleCardProps) {
  const { gameState } = useGame()
  const isCompleted = gameState.completedModules.includes(module.id)
  const progress = gameState.moduleProgress[module.id] || 0
  const progressPercentage = (progress / module.chapters) * 100
  
  // Determine if module is unlocked based on prerequisites
  const isUnlocked = () => {
    switch (module.id) {
      case 'foundations':
        return true // Always unlocked
      case 'instagram':
        return gameState.completedModules.includes('foundations')
      case 'facebook':
        return gameState.completedModules.includes('instagram')
      case 'google':
        return gameState.completedModules.includes('facebook')
      default:
        return false
    }
  }

  const unlocked = isUnlocked()

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner':
        return 'bg-green-100 text-green-800'
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800'
      case 'Advanced':
        return 'bg-orange-100 text-orange-800'
      case 'Expert':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const cardContent = (
    <motion.div
      whileHover={unlocked ? { y: -5, scale: 1.02 } : {}}
      transition={{ duration: 0.2 }}
      className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 ${
        unlocked
          ? 'border-gray-200 hover:border-primary-300 hover:shadow-xl bg-white'
          : 'border-gray-300 bg-gray-50 opacity-75'
      }`}
    >
      {/* Header with gradient background */}
      <div className={`h-28 sm:h-32 bg-gradient-to-r ${module.color} relative overflow-hidden`}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative p-4 sm:p-6 h-full flex items-center justify-between text-white">
          <div>
            <div className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-medium mb-1 sm:mb-2 ${getLevelColor(module.level)}`}>
              {module.level}
            </div>
            <h3 className="text-lg sm:text-xl font-bold">{module.title}</h3>
          </div>
          <div className="text-right">
            <module.icon className="w-6 h-6 sm:w-8 sm:h-8 mb-1 sm:mb-2" />
            {!unlocked && <Lock className="w-5 h-5 sm:w-6 sm:h-6 opacity-75" />}
            {isCompleted && <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-green-300" />}
          </div>
        </div>
        
        {/* Progress bar for unlocked modules */}
        {unlocked && progress > 0 && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/30">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${progressPercentage}%` }}
              transition={{ duration: 1, delay: 0.5 }}
              className="h-full bg-white"
            />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4 sm:p-6">
        <p className={`text-gray-600 mb-3 sm:mb-4 leading-relaxed text-sm sm:text-base ${!unlocked ? 'text-gray-500' : ''}`}>
          {module.description}
        </p>

        {/* Module Stats */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 text-xs sm:text-sm space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="flex items-center space-x-1 text-gray-500">
              <BookOpen className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>{module.chapters} chapters</span>
            </div>
            <div className="flex items-center space-x-1 text-gray-500">
              <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>{module.estimatedTime}</span>
            </div>
          </div>
          {unlocked && progress > 0 && (
            <div className="text-primary-600 font-medium text-xs sm:text-sm">
              {progress}/{module.chapters} completed
            </div>
          )}
        </div>

        {/* Topics */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">What you'll learn:</h4>
          <div className="flex flex-wrap gap-2">
            {module.topics.map((topic, index) => (
              <span
                key={index}
                className={`px-2 py-1 text-xs rounded-full ${
                  unlocked
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-200 text-gray-500'
                }`}
              >
                {topic}
              </span>
            ))}
          </div>
        </div>

        {/* Action Button */}
        <div className="flex items-center justify-between">
          {unlocked ? (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center space-x-2 px-3 sm:px-4 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base min-h-[44px] ${
                isCompleted
                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                  : progress > 0
                  ? 'bg-primary-100 text-primary-700 hover:bg-primary-200'
                  : 'bg-primary-600 text-white hover:bg-primary-700'
              }`}
            >
              <Play className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>
                {isCompleted ? 'Review' : progress > 0 ? 'Continue' : 'Start'}
              </span>
            </motion.button>
          ) : (
            <div className="flex items-center space-x-2 px-3 sm:px-4 py-2 sm:py-3 rounded-lg bg-gray-200 text-gray-500 text-sm sm:text-base min-h-[44px]">
              <Lock className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Locked</span>
            </div>
          )}

          {isCompleted && (
            <div className="flex items-center space-x-1 text-green-600">
              <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="text-xs sm:text-sm font-medium">Completed</span>
            </div>
          )}
        </div>

        {/* Unlock requirement for locked modules */}
        {!unlocked && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              Complete the previous module to unlock this content.
            </p>
          </div>
        )}
      </div>
    </motion.div>
  )

  return unlocked ? (
    <Link href={`/modules/${module.id}`}>
      {cardContent}
    </Link>
  ) : (
    cardContent
  )
}
