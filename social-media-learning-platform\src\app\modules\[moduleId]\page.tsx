'use client'

import { motion } from 'framer-motion'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON><PERSON><PERSON>, Clock, Trophy, CheckCircle, Play, Lock, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { useGame } from '@/contexts/GameContext'

const moduleData = {
  foundations: {
    title: 'Social Media Foundations',
    description: 'Master the fundamentals of social media marketing, sales funnels, and essential tools.',
    color: 'from-blue-500 to-cyan-500',
    chapters: [
      { id: 'importance', title: 'Importance of Social Media Marketing', duration: '25 min', xp: 100 },
      { id: 'sales-funnel', title: 'Creating the Sales Funnel', duration: '30 min', xp: 100 },
      { id: 'local-benefits', title: 'Benefits of Social Media for Local Businesses', duration: '20 min', xp: 100 },
      { id: 'tools-plugins', title: 'Tools and Plugins', duration: '35 min', xp: 100 },
      { id: 'instagram-basics', title: 'Instagram Marketing', duration: '40 min', xp: 100 },
      { id: 'twitter-marketing', title: 'Twitter Marketing', duration: '30 min', xp: 100 },
      { id: 'facebook-marketing', title: 'Facebook Marketing', duration: '45 min', xp: 100 },
      { id: 'reddit-facebook-ads', title: 'Reddit And Facebook Ads Strategy', duration: '35 min', xp: 100 },
      { id: 'snapchat', title: 'Snapchat', duration: '25 min', xp: 100 },
      { id: 'google-platform', title: 'Google Platform', duration: '40 min', xp: 100 },
      { id: 'linkedin', title: 'LinkedIn', duration: '30 min', xp: 100 },
      { id: 'pinterest', title: 'Pinterest', duration: '25 min', xp: 100 }
    ]
  },
  instagram: {
    title: 'Instagram Mastery',
    description: 'Become an Instagram expert with advanced strategies, influencer marketing, and content creation.',
    color: 'from-pink-500 to-purple-500',
    chapters: [
      { id: 'instagram-marketing', title: 'How to Use Instagram for Marketing', duration: '30 min', xp: 120 },
      { id: 'getting-started', title: 'Getting Started on Instagram', duration: '25 min', xp: 120 },
      { id: 'branded-account', title: 'Creating A Branded Account', duration: '35 min', xp: 120 },
      { id: 'marketing-strategy', title: 'The Effective Instagram Marketing Strategy', duration: '40 min', xp: 120 },
      { id: 'influencers', title: 'The Importance of Influencers', duration: '30 min', xp: 120 },
      { id: 'finding-influencers', title: 'How to Search for An Influencer', duration: '35 min', xp: 120 },
      { id: 'unwritten-rules', title: 'The 5 Unwritten Rules of Instagram', duration: '25 min', xp: 120 },
      { id: 'photo-hacks', title: 'Hacks for Taking Good Photos', duration: '45 min', xp: 120 },
      { id: 'automation', title: 'Instagram Automation', duration: '30 min', xp: 120 },
      { id: 'instagram-ads', title: 'Taking Advantage of Instagram Ads', duration: '50 min', xp: 120 },
      { id: 'retargeting', title: 'Retargeting Customers on Instagram', duration: '35 min', xp: 120 },
      { id: 'location', title: 'Use Location (Geotag)', duration: '20 min', xp: 120 },
      { id: 'contests', title: 'Organize Contests and Giveaways', duration: '40 min', xp: 120 },
      { id: 'engagement-secrets', title: 'The 14 Secrets to Engaging Your Audience', duration: '45 min', xp: 120 },
      { id: 'analytics', title: 'Understanding Analytics', duration: '35 min', xp: 120 }
    ]
  },
  facebook: {
    title: 'Facebook Advertising Pro',
    description: 'Master Facebook advertising, business pages, targeting, and campaign optimization.',
    color: 'from-blue-600 to-indigo-600',
    chapters: [
      { id: 'facebook-pages', title: 'Everything about Facebook Pages', duration: '35 min', xp: 140 },
      { id: 'insights', title: 'Facebook Insights', duration: '30 min', xp: 140 },
      { id: 'branding', title: 'Branding', duration: '25 min', xp: 140 },
      { id: 'seo-strategies', title: 'Strategies for Search Engine Optimization', duration: '40 min', xp: 140 },
      { id: 'facebook-advertising', title: 'Facebook Advertising', duration: '50 min', xp: 140 },
      { id: 'two-way-marketing', title: 'Marketing is a two-way street', duration: '30 min', xp: 140 },
      { id: 'pre-selling', title: 'Pre-selling your audience', duration: '35 min', xp: 140 },
      { id: 'sales-funnel', title: 'Sales Funnel', duration: '45 min', xp: 140 },
      { id: 'improve-test-grow', title: 'Improve, Test, Grow, and Monetize', duration: '40 min', xp: 140 },
      { id: 'analyzing-retargeting', title: 'Analyzing and Retargeting', duration: '45 min', xp: 140 },
      { id: 'scheduling', title: 'Scheduling', duration: '25 min', xp: 140 },
      { id: 'organic-reach', title: 'Maximizing Organic Reach on Facebook', duration: '35 min', xp: 140 },
      { id: 'cpa-focus', title: 'Focus on Cost Per Action (CPA)', duration: '40 min', xp: 140 },
      { id: 'pixel-targeting', title: 'Using the Pixel to improve Ad Targeting', duration: '45 min', xp: 140 },
      { id: 'common-mistakes', title: 'Common mistakes and How to Avoid Them', duration: '30 min', xp: 140 }
    ]
  },
  google: {
    title: 'Google AdWords Expert',
    description: 'Dominate Google advertising with SEO, analytics, and advanced campaign strategies.',
    color: 'from-green-500 to-emerald-500',
    chapters: [
      { id: 'choosing-keywords', title: 'Choosing Keywords', duration: '40 min', xp: 160 },
      { id: 'local-seo', title: 'Local SEO Begins at Home', duration: '35 min', xp: 160 },
      { id: 'ppc-vs-seo', title: 'PPC vs. SEO', duration: '30 min', xp: 160 },
      { id: 'keyword-content', title: 'Website Content that is Keyword Specific', duration: '45 min', xp: 160 },
      { id: 'quality-score', title: 'Google Quality Score', duration: '35 min', xp: 160 },
      { id: 'compelling-ads', title: 'Creating Compelling Ads', duration: '40 min', xp: 160 },
      { id: 'conversion-optimization', title: 'Optimizing for Conversions', duration: '45 min', xp: 160 },
      { id: 'content-marketing', title: 'Tips for your Content Marketing Strategy', duration: '35 min', xp: 160 },
      { id: 'ad-group-settings', title: 'Search Campaigns Ad Group Settings & structure', duration: '40 min', xp: 160 },
      { id: 'facebook-paid-ads', title: 'Facebook and paid advertising', duration: '35 min', xp: 160 },
      { id: 'youtube', title: 'YouTube', duration: '45 min', xp: 160 },
      { id: 'converting-followers', title: 'Converting Your Followers', duration: '30 min', xp: 160 },
      { id: 'landing-pages', title: 'Making Your Landing Page Effective', duration: '40 min', xp: 160 },
      { id: 'campaign-optimization', title: 'How to Optimize Your AdWords Campaign', duration: '45 min', xp: 160 }
    ]
  }
}



export default function ModulePage() {
  const params = useParams()
  const router = useRouter()
  const moduleId = params.moduleId as string
  const { gameState, setCurrentModule } = useGame()

  const module = moduleData[moduleId as keyof typeof moduleData]
  
  if (!module) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Module Not Found</h1>
          <Link href="/modules">
            <button type="button" className="btn-primary">Back to Modules</button>
          </Link>
        </div>
      </div>
    )
  }

  // Filter completed chapters using URL format (foundations-chapter-1, etc.)
  const completedChapters = gameState.completedChapters.filter(ch =>
    ch.startsWith(`${moduleId}-chapter-`)
  )
  const progress = completedChapters.length
  const progressPercentage = (progress / module.chapters.length) * 100
  const isModuleCompleted = gameState.completedModules.includes(moduleId)

  console.log('📊 Module progress calculation:', {
    moduleId,
    allCompletedChapters: gameState.completedChapters,
    filteredCompletedChapters: completedChapters,
    progress,
    totalChapters: module.chapters.length,
    progressPercentage
  })
  
  // Check if module is unlocked
  const isUnlocked = () => {
    switch (moduleId) {
      case 'foundations':
        return true
      case 'instagram':
        return gameState.completedModules.includes('foundations')
      case 'facebook':
        return gameState.completedModules.includes('instagram')
      case 'google':
        return gameState.completedModules.includes('facebook')
      default:
        return false
    }
  }

  const unlocked = isUnlocked()

  if (!unlocked) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center bg-white rounded-xl p-12 shadow-lg max-w-md"
        >
          <Lock className="w-16 h-16 mx-auto mb-6 text-gray-400" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Module Locked</h1>
          <p className="text-gray-600 mb-6">
            Complete the previous module to unlock this content.
          </p>
          <Link href="/modules">
            <button type="button" className="btn-primary">Back to Modules</button>
          </Link>
        </motion.div>
      </div>
    )
  }

  const handleChapterClick = (chapterId: string) => {
    // Navigate to chapter page using the chapter number format
    setCurrentModule(moduleId)

    // Find the chapter index to create the correct URL
    const chapterIndex = module.chapters.findIndex(ch => ch.id === chapterId)
    const chapterNumber = chapterIndex + 1
    const urlChapterId = `chapter-${chapterNumber}`

    console.log('🔗 Chapter navigation:', {
      chapterId,
      chapterIndex,
      chapterNumber,
      urlChapterId,
      finalUrl: `/modules/${moduleId}/${urlChapterId}`
    })

    router.push(`/modules/${moduleId}/${urlChapterId}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <section className={`py-16 bg-gradient-to-r ${module.color} text-white`}>
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Link href="/modules" className="inline-flex items-center space-x-2 text-white/80 hover:text-white mb-6 transition-colors">
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Modules</span>
            </Link>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{module.title}</h1>
            <p className="text-xl text-blue-100 mb-6 max-w-3xl">{module.description}</p>
            
            {/* Progress Stats */}
            <div className="flex items-center space-x-8 text-blue-100">
              <div className="flex items-center space-x-2">
                <BookOpen className="w-5 h-5" />
                <span>{module.chapters.length} chapters</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span>{progress} completed</span>
              </div>
              <div className="flex items-center space-x-2">
                <Trophy className="w-5 h-5" />
                <span>{Math.round(progressPercentage)}% progress</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Progress Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-xl p-8 shadow-lg mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Module Progress</h2>
            {isModuleCompleted && (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="w-6 h-6" />
                <span className="font-semibold">Completed!</span>
              </div>
            )}
          </div>
          
          <div className="progress-bar h-4 mb-4">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${progressPercentage}%` }}
              transition={{ duration: 1.5, delay: 0.5 }}
              className={`h-full bg-gradient-to-r ${module.color} rounded-full`}
            />
          </div>
          
          <div className="flex justify-between text-sm text-gray-600">
            <span>{progress} of {module.chapters.length} chapters completed</span>
            <span>{Math.round(progressPercentage)}% complete</span>
          </div>
        </motion.div>

        {/* Chapters List */}
        <div className="grid gap-4">
          {module.chapters.map((chapter, index) => {
            // Use URL format for checking completion
            const chapterUrlKey = `${moduleId}-chapter-${index + 1}`
            const isCompleted = gameState.completedChapters.includes(chapterUrlKey)
            const prevChapterUrlKey = index > 0 ? `${moduleId}-chapter-${index}` : null
            const isAccessible = index === 0 || (prevChapterUrlKey && gameState.completedChapters.includes(prevChapterUrlKey))

            console.log('📋 Chapter completion check:', {
              chapterTitle: chapter.title,
              chapterUrlKey,
              isCompleted,
              completedChapters: gameState.completedChapters,
              isAccessible
            })
            
            return (
              <motion.div
                key={chapter.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                className={`bg-white rounded-xl border-2 transition-all duration-300 ${
                  isCompleted
                    ? 'border-green-300 bg-green-50'
                    : isAccessible
                    ? 'border-gray-200 hover:border-primary-300 hover:shadow-lg'
                    : 'border-gray-200 bg-gray-50 opacity-60'
                }`}
              >
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        isCompleted
                          ? 'bg-green-500 text-white'
                          : isAccessible
                          ? 'bg-primary-100 text-primary-600'
                          : 'bg-gray-200 text-gray-500'
                      }`}>
                        {isCompleted ? (
                          <CheckCircle className="w-6 h-6" />
                        ) : isAccessible ? (
                          <Play className="w-5 h-5" />
                        ) : (
                          <Lock className="w-5 h-5" />
                        )}
                      </div>
                      
                      <div>
                        <h3 className={`text-lg font-semibold ${
                          isAccessible ? 'text-gray-900' : 'text-gray-500'
                        }`}>
                          Chapter {index + 1}: {chapter.title}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Clock className="w-4 h-4" />
                            <span>{chapter.duration}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Trophy className="w-4 h-4" />
                            <span>{chapter.xp} XP</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {isAccessible && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleChapterClick(chapter.id)}
                        className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                          isCompleted
                            ? 'bg-green-100 text-green-700 hover:bg-green-200'
                            : 'bg-primary-600 text-white hover:bg-primary-700'
                        }`}
                      >
                        {isCompleted ? 'Review' : 'Start Chapter'}
                      </motion.button>
                    )}
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Module Completion */}
        {isModuleCompleted && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl p-8 text-white text-center"
          >
            <Trophy className="w-16 h-16 mx-auto mb-4" />
            <h2 className="text-3xl font-bold mb-4">Congratulations!</h2>
            <p className="text-xl text-green-100 mb-6">
              You've successfully completed the {module.title} module!
            </p>
            <Link href="/modules">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Continue to Next Module
              </motion.button>
            </Link>
          </motion.div>
        )}
      </div>
    </div>
  )
}
