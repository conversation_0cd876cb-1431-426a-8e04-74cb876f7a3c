# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/
.next

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Large audio files (use placeholder or external hosting)
*.mp3
*.wav
*.ogg
*.m4a
*.flac

# Large binary files
*.node

# Audio files in public directory
public/audio/*.mp3
public/audio/*.wav
public/audio/*.ogg

# Specific large files
"Chill Lofi Mix [chill lo-fi hip hop beats].mp3"
**/next-swc.*.node

# Next.js specific large files
**/@next/swc-*/next-swc.*.node
