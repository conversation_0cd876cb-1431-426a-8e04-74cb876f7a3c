'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Volume2, VolumeX, Music, Settings, X, Play } from 'lucide-react'
import { useAudio } from '@/hooks/useAudio'

interface AudioSettingsProps {
  isOpen: boolean
  onClose: () => void
}

export function AudioSettings({ isOpen, onClose }: AudioSettingsProps) {
  const { settings, updateSettings, playFeedback, toggleBackgroundMusic, isBackgroundMusicPlaying } = useAudio()
  const [tempSettings, setTempSettings] = useState(settings)

  const handleSave = () => {
    updateSettings(tempSettings)
    playFeedback('success')
    onClose()
  }

  const handleReset = () => {
    const defaultSettings = {
      enabled: true,
      volume: 0.7,
      ambientEnabled: false,
      ambientVolume: 0.3,
      backgroundMusicEnabled: false,
      backgroundMusicVolume: 0.4
    }
    setTempSettings(defaultSettings)
    playFeedback('click')
  }

  const testSound = (type: 'click' | 'success' | 'achievement' | 'levelUp') => {
    playFeedback(type)
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl p-6 max-w-md w-full shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Settings className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Audio Settings</h2>
                  <p className="text-sm text-gray-600">Customize your audio experience</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Settings */}
            <div className="space-y-6">
              {/* Master Audio Toggle */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {tempSettings.enabled ? (
                    <Volume2 className="w-5 h-5 text-blue-500" />
                  ) : (
                    <VolumeX className="w-5 h-5 text-gray-400" />
                  )}
                  <div>
                    <div className="font-medium text-gray-900">Sound Effects</div>
                    <div className="text-sm text-gray-600">Enable audio feedback</div>
                  </div>
                </div>
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    const newEnabled = !tempSettings.enabled
                    setTempSettings(prev => ({ ...prev, enabled: newEnabled }))
                    // Immediately update the actual settings
                    updateSettings({ enabled: newEnabled })
                    playFeedback('click')
                  }}
                  className={`relative w-12 h-6 rounded-full transition-colors ${
                    tempSettings.enabled ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                >
                  <motion.div
                    animate={{ x: tempSettings.enabled ? 24 : 0 }}
                    transition={{ duration: 0.2 }}
                    className="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md"
                  />
                </motion.button>
              </div>

              {/* Volume Control */}
              {tempSettings.enabled && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Volume</span>
                    <span className="text-sm text-gray-500">{Math.round(tempSettings.volume * 100)}%</span>
                  </div>
                  <div className="relative">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={tempSettings.volume}
                      onChange={(e) => {
                        const newVolume = parseFloat(e.target.value)
                        setTempSettings(prev => ({ ...prev, volume: newVolume }))
                        // Immediately update for real-time feedback
                        updateSettings({ volume: newVolume })
                      }}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      title="Sound Effects Volume"
                    />
                  </div>
                </motion.div>
              )}

              {/* Background Music */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Music className={`w-5 h-5 ${tempSettings.backgroundMusicEnabled ? 'text-purple-500' : 'text-gray-400'}`} />
                  <div>
                    <div className="font-medium text-gray-900">Background Music</div>
                    <div className="text-sm text-gray-600">Lofi hip-hop beats while learning</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      // Enable background music if it's not enabled
                      if (!tempSettings.backgroundMusicEnabled) {
                        const newSettings = { ...tempSettings, backgroundMusicEnabled: true }
                        setTempSettings(newSettings)
                        updateSettings({ backgroundMusicEnabled: true })
                      }
                      // Then toggle play/pause
                      toggleBackgroundMusic()
                      playFeedback('click')
                    }}
                    className="p-2 bg-purple-100 hover:bg-purple-200 rounded-lg transition-colors"
                    title={isBackgroundMusicPlaying ? 'Pause Music' : 'Play Music'}
                  >
                    {isBackgroundMusicPlaying ? '⏸️' : '▶️'}
                  </motion.button>
                  <motion.button
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      const newEnabled = !tempSettings.backgroundMusicEnabled
                      setTempSettings(prev => ({ ...prev, backgroundMusicEnabled: newEnabled }))
                      // Immediately update the actual settings
                      updateSettings({ backgroundMusicEnabled: newEnabled })
                      playFeedback('click')
                    }}
                    className={`relative w-12 h-6 rounded-full transition-colors ${
                      tempSettings.backgroundMusicEnabled ? 'bg-purple-500' : 'bg-gray-300'
                    }`}
                  >
                    <motion.div
                      animate={{ x: tempSettings.backgroundMusicEnabled ? 24 : 0 }}
                      transition={{ duration: 0.2 }}
                      className="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md"
                    />
                  </motion.button>
                </div>
              </div>

              {/* Background Music Volume */}
              {tempSettings.backgroundMusicEnabled && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Music Volume</span>
                    <span className="text-sm text-gray-500">{Math.round(tempSettings.backgroundMusicVolume * 100)}%</span>
                  </div>
                  <div className="relative">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={tempSettings.backgroundMusicVolume}
                      onChange={(e) => {
                        const newVolume = parseFloat(e.target.value)
                        setTempSettings(prev => ({ ...prev, backgroundMusicVolume: newVolume }))
                        // Immediately update for real-time feedback
                        updateSettings({ backgroundMusicVolume: newVolume })
                      }}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      title="Background Music Volume"
                    />
                  </div>
                </motion.div>
              )}

              {/* Ambient Music */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Music className={`w-5 h-5 ${tempSettings.ambientEnabled ? 'text-blue-500' : 'text-gray-400'}`} />
                  <div>
                    <div className="font-medium text-gray-900">Ambient Sounds</div>
                    <div className="text-sm text-gray-600">Synthesized ambient tones</div>
                  </div>
                </div>
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    const newEnabled = !tempSettings.ambientEnabled
                    setTempSettings(prev => ({ ...prev, ambientEnabled: newEnabled }))
                    // Immediately update the actual settings
                    updateSettings({ ambientEnabled: newEnabled })
                    playFeedback('click')
                  }}
                  className={`relative w-12 h-6 rounded-full transition-colors ${
                    tempSettings.ambientEnabled ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                >
                  <motion.div
                    animate={{ x: tempSettings.ambientEnabled ? 24 : 0 }}
                    transition={{ duration: 0.2 }}
                    className="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md"
                  />
                </motion.button>
              </div>

              {/* Ambient Volume */}
              {tempSettings.ambientEnabled && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Ambient Volume</span>
                    <span className="text-sm text-gray-500">{Math.round(tempSettings.ambientVolume * 100)}%</span>
                  </div>
                  <div className="relative">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={tempSettings.ambientVolume}
                      onChange={(e) => {
                        const newVolume = parseFloat(e.target.value)
                        setTempSettings(prev => ({ ...prev, ambientVolume: newVolume }))
                        // Immediately update for real-time feedback
                        updateSettings({ ambientVolume: newVolume })
                      }}
                      title="Ambient Volume"
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                </motion.div>
              )}

              {/* Sound Test */}
              {tempSettings.enabled && (
                <div className="border-t pt-4">
                  <div className="text-sm font-medium text-gray-700 mb-3">Test Sounds</div>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { type: 'click' as const, label: 'Click', icon: '🔘' },
                      { type: 'success' as const, label: 'Success', icon: '✅' },
                      { type: 'achievement' as const, label: 'Achievement', icon: '🏆' },
                      { type: 'levelUp' as const, label: 'Level Up', icon: '⭐' }
                    ].map((sound) => (
                      <motion.button
                        key={sound.type}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => testSound(sound.type)}
                        className="flex items-center space-x-2 p-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm"
                      >
                        <span>{sound.icon}</span>
                        <span>{sound.label}</span>
                        <Play className="w-3 h-3 ml-auto" />
                      </motion.button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between mt-8 pt-6 border-t">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleReset}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
              >
                Reset to Default
              </motion.button>
              
              <div className="flex space-x-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleSave}
                  className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 font-medium"
                >
                  Save
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Floating Audio Control Button - Single comprehensive control
export function AudioControlButton() {
  const { settings, playFeedback, toggleBackgroundMusic, isBackgroundMusicPlaying } = useAudio()
  const [showSettings, setShowSettings] = useState(false)
  const [showQuickControls, setShowQuickControls] = useState(false)

  return (
    <>
      {/* Main Audio Control Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <AnimatePresence>
          {showQuickControls && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.8 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.8 }}
              className="absolute bottom-16 right-0 bg-white rounded-xl shadow-2xl p-4 min-w-[200px] border"
            >
              {/* Quick Music Toggle */}
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">Background Music</span>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    // Enable background music if it's not enabled
                    if (!settings.backgroundMusicEnabled) {
                      updateSettings({ backgroundMusicEnabled: true })
                    }
                    // Then toggle play/pause
                    toggleBackgroundMusic()
                    playFeedback('click')
                  }}
                  className={`p-2 rounded-lg transition-colors ${
                    isBackgroundMusicPlaying
                      ? 'bg-purple-100 text-purple-600'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                  title={isBackgroundMusicPlaying ? 'Pause Music' : 'Play Music'}
                >
                  {isBackgroundMusicPlaying ? '⏸️' : '▶️'}
                </motion.button>
              </div>

              {/* Settings Button */}
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  setShowSettings(true)
                  setShowQuickControls(false)
                  playFeedback('click')
                }}
                className="w-full flex items-center space-x-2 p-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm"
              >
                <Settings className="w-4 h-4" />
                <span>Audio Settings</span>
              </motion.button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => {
            setShowQuickControls(!showQuickControls)
            playFeedback('click')
          }}
          className="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center relative"
          title="Audio Controls"
        >
          {settings.enabled ? (
            <Volume2 className="w-6 h-6" />
          ) : (
            <VolumeX className="w-6 h-6" />
          )}

          {/* Music Playing Indicator */}
          {isBackgroundMusicPlaying && (
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
              className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"
            />
          )}
        </motion.button>
      </div>

      <AudioSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </>
  )
}
