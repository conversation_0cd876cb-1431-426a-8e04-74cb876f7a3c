'use client'

import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { createClient } from '@/utils/supabase/client'
import { SupabaseService, type UserProfile } from '@/lib/supabase'

export interface User extends UserProfile {
  // Extending UserProfile from Supabase
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: User }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'CLEAR_ERROR' }

const initialState: AuthState = {
  user: null,
  isLoading: false,
  isAuthenticated: false,
  error: null
}

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null
      }
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }
    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload
      }
    case 'AUTH_LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      }
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null
      }
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      }
    default:
      return state
  }
}

const AuthContext = createContext<{
  authState: AuthState
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, name: string) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  clearError: () => void
} | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, dispatch] = useReducer(authReducer, initialState)
  const supabase = createClient()

  // Load user from Supabase on mount
  useEffect(() => {
    const getInitialSession = async () => {
      dispatch({ type: 'AUTH_START' })

      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          // Get user profile from database
          const profile = await SupabaseService.getUserProfile(user.id)
          if (profile) {
            dispatch({ type: 'AUTH_SUCCESS', payload: profile })
          } else {
            // Create profile if it doesn't exist
            const newProfile = await SupabaseService.createUserProfile({
              id: user.id,
              email: user.email!,
              full_name: user.user_metadata?.full_name || user.email?.split('@')[0],
              preferences: {
                theme: 'light',
                notifications: true,
                emailUpdates: true,
                audioEnabled: true
              },
              subscription_type: 'free'
            })

            if (newProfile) {
              dispatch({ type: 'AUTH_SUCCESS', payload: newProfile })
            }
          }
        }
      } catch (error) {
        console.error('Failed to load user:', error)
        dispatch({ type: 'AUTH_ERROR', payload: 'Failed to load user session' })
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        const profile = await SupabaseService.getUserProfile(session.user.id)
        if (profile) {
          dispatch({ type: 'AUTH_SUCCESS', payload: profile })
        }
      } else if (event === 'SIGNED_OUT') {
        dispatch({ type: 'AUTH_LOGOUT' })
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  // Save user to localStorage whenever auth state changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (authState.user) {
        localStorage.setItem('socialMediaUser', JSON.stringify(authState.user))
      } else {
        localStorage.removeItem('socialMediaUser')
      }
    }
  }, [authState.user])

  const signIn = async (email: string, password: string) => {
    dispatch({ type: 'AUTH_START' })

    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password })

      if (error) {
        throw new Error(error.message)
      }

      if (data.user) {
        const profile = await SupabaseService.getUserProfile(data.user.id)
        if (profile) {
          dispatch({ type: 'AUTH_SUCCESS', payload: profile })
        }
      }
    } catch (error: any) {
      dispatch({ type: 'AUTH_ERROR', payload: error.message || 'Invalid email or password' })
    }
  }

  const signUp = async (email: string, password: string, name: string) => {
    dispatch({ type: 'AUTH_START' })

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { full_name: name }
        }
      })

      if (error) {
        throw new Error(error.message)
      }

      if (data.user) {
        // Create user profile
        const profile = await SupabaseService.createUserProfile({
          id: data.user.id,
          email: data.user.email!,
          full_name: name,
          preferences: {
            theme: 'light',
            notifications: true,
            emailUpdates: true,
            audioEnabled: true
          },
          subscription_type: 'free'
        })

        if (profile) {
          dispatch({ type: 'AUTH_SUCCESS', payload: profile })
        }
      }
    } catch (error: any) {
      dispatch({ type: 'AUTH_ERROR', payload: error.message || 'Failed to create account' })
    }
  }

  const signOut = async () => {
    try {
      console.log('🚪 Starting sign-out process...')

      // Clear localStorage data
      if (typeof window !== 'undefined') {
        localStorage.removeItem('socialMediaGameState')
        localStorage.removeItem('audioSettings')
        localStorage.removeItem('user')
        console.log('🧹 Cleared localStorage data')
      }

      // Sign out from Supabase
      await supabase.auth.signOut()
      dispatch({ type: 'AUTH_LOGOUT' })

      console.log('✅ Sign-out completed successfully')

      // Redirect to home page
      if (typeof window !== 'undefined') {
        window.location.href = '/'
      }
    } catch (error) {
      console.error('❌ Sign out error:', error)
      // Even if there's an error, clear local state
      dispatch({ type: 'AUTH_LOGOUT' })
      if (typeof window !== 'undefined') {
        localStorage.clear()
        window.location.href = '/'
      }
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email)
      if (error) {
        throw new Error(error.message)
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send reset email')
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    try {
      // Simulate API call to update user profile
      await new Promise(resolve => setTimeout(resolve, 500))
      dispatch({ type: 'UPDATE_USER', payload: updates })
    } catch (error) {
      throw new Error('Failed to update profile')
    }
  }

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  return (
    <AuthContext.Provider value={{
      authState,
      signIn,
      signUp,
      signOut,
      resetPassword,
      updateProfile,
      clearError
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
