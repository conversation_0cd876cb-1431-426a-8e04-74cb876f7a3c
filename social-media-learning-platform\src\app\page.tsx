'use client'

import { motion } from 'framer-motion'
import { Trophy, Target, BookOpen, Users, Star, Zap, Lock, Play } from 'lucide-react'
import Link from 'next/link'
import { useGame } from '@/contexts/GameContext'
import { useAuth } from '@/contexts/AuthContext'
import { useAudio } from '@/hooks/useAudio'
import { ModuleCard } from '@/components/LearningModules/ModuleCard'
import { ProgressOverview } from '@/components/GameElements/ProgressOverview'
import { AchievementShowcase } from '@/components/GameElements/AchievementShowcase'
import { AuthModal } from '@/components/Auth/AuthModal'
import { ParticleSystem } from '@/components/Animations/ParticleSystem'
import { useState } from 'react'

export default function HomePage() {
  const { gameState } = useGame()
  const { authState } = useAuth()
  const { playFeedback } = useAudio()
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)

  const modules = [
    {
      id: 'foundations',
      title: 'Social Media Foundations',
      description: 'Master the fundamentals of social media marketing, sales funnels, and essential tools.',
      level: 'Beginner',
      chapters: 12,
      estimatedTime: '4-6 hours',
      color: 'from-blue-500 to-cyan-500',
      icon: BookOpen,
      topics: ['Platform Importance', 'Sales Funnels', 'Local Business Benefits', 'Essential Tools']
    },
    {
      id: 'instagram',
      title: 'Instagram Mastery',
      description: 'Become an Instagram expert with advanced strategies, influencer marketing, and content creation.',
      level: 'Intermediate',
      chapters: 15,
      estimatedTime: '6-8 hours',
      color: 'from-pink-500 to-purple-500',
      icon: Target,
      topics: ['Content Strategy', 'Influencer Marketing', 'Instagram Ads', 'Analytics']
    },
    {
      id: 'facebook',
      title: 'Facebook Advertising Pro',
      description: 'Master Facebook advertising, business pages, targeting, and campaign optimization.',
      level: 'Advanced',
      chapters: 15,
      estimatedTime: '6-8 hours',
      color: 'from-blue-600 to-indigo-600',
      icon: Users,
      topics: ['Business Pages', 'Ads Manager', 'Targeting', 'Optimization']
    },
    {
      id: 'google',
      title: 'Google AdWords Expert',
      description: 'Dominate Google advertising with SEO, analytics, and advanced campaign strategies.',
      level: 'Expert',
      chapters: 14,
      estimatedTime: '5-7 hours',
      color: 'from-green-500 to-emerald-500',
      icon: Zap,
      topics: ['Keyword Research', 'SEO', 'Campaign Optimization', 'Landing Pages']
    }
  ]

  const stats = [
    { label: 'Total XP', value: gameState.totalXP, icon: Star },
    { label: 'Level', value: gameState.level, icon: Trophy },
    { label: 'Modules Completed', value: gameState.completedModules.length, icon: BookOpen },
    { label: 'Achievements', value: gameState.achievements.length, icon: Target }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 text-white min-h-screen flex items-center">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 bg-hero-pattern opacity-10"></div>

        {/* Premium Floating Geometric Shapes */}
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl hidden lg:block"
        />
        <motion.div
          animate={{
            y: [20, -20, 20],
            rotate: [360, 180, 0],
            x: [-10, 10, -10],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-40 right-20 w-24 h-24 bg-accent-300/20 rounded-lg blur-lg hidden lg:block"
        />
        <motion.div
          animate={{
            y: [-30, 30, -30],
            x: [-10, 10, -10],
            scale: [1, 1.2, 1],
            rotate: [0, 90, 180, 270, 360],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute bottom-32 left-1/4 w-20 h-20 bg-primary-300/30 rounded-full blur-md hidden lg:block"
        />
        <motion.div
          animate={{
            y: [15, -15, 15],
            x: [10, -10, 10],
            rotate: [0, 45, 90, 135, 180, 225, 270, 315, 360],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/3 right-1/3 w-16 h-16 bg-white/15 transform rotate-45 blur-sm hidden lg:block"
        />

        {/* Particle Grid Animation */}
        <div className="absolute inset-0 opacity-30 hidden md:block">
          {Array.from({ length: 30 }).map((_, i) => (
            <motion.div
              key={i}
              initial={{
                x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1200),
                y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800),
                scale: 0,
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 0.6, 0],
                y: [
                  Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800),
                  Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800) - 100,
                  Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800),
                ],
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "easeInOut"
              }}
              className="absolute w-1 h-1 bg-white rounded-full"
            />
          ))}
        </div>

        <div className="relative container mx-auto px-4 py-12 sm:py-16 md:py-20 z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            {/* Enhanced Title with Staggered Animation */}
            <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                Social Media
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{
                  duration: 0.8,
                  delay: 0.5,
                  type: "spring",
                  stiffness: 100
                }}
                className="block gradient-text bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent px-2"
              >
                Mastery Quest
              </motion.div>
            </div>

            {/* Enhanced Description with Typewriter Effect */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="text-base sm:text-lg md:text-xl lg:text-2xl mb-6 sm:mb-8 text-blue-100 leading-relaxed px-4"
            >
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 2, delay: 1 }}
                className="inline-block"
              >
                Transform your social media marketing skills through an interactive, gamified learning experience.
              </motion.span>
              <br />
              <motion.span
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 2.5 }}
                className="inline-block"
              >
                Master Instagram, Facebook, Google AdWords, and more!
              </motion.span>
            </motion.div>
            {/* Enhanced Button Section with Staggered Animation */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 3 }}
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4"
            >
              {authState.isAuthenticated ? (
                <>
                  <motion.div
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 3.2 }}
                  >
                    <Link href="/dashboard">
                      <motion.button
                        whileHover={{
                          scale: 1.05,
                          boxShadow: "0 10px 25px rgba(0,0,0,0.2)",
                          y: -2
                        }}
                        whileTap={{ scale: 0.95 }}
                        onMouseEnter={() => playFeedback('buttonHover')}
                        onClick={() => playFeedback('click')}
                        className="w-full sm:w-auto btn-primary text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 bg-white text-blue-600 hover:bg-gray-100 min-h-[44px] flex items-center justify-center space-x-2 shadow-lg transition-all duration-300"
                      >
                        <motion.div
                          animate={{ rotate: [0, 360] }}
                          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        >
                          <Play className="w-5 h-5" />
                        </motion.div>
                        <span>Continue Quest</span>
                      </motion.button>
                    </Link>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 3.4 }}
                  >
                    <Link href="/modules">
                      <motion.button
                        whileHover={{
                          scale: 1.05,
                          backgroundColor: "rgba(255,255,255,0.1)",
                          borderColor: "rgba(255,255,255,0.8)"
                        }}
                        whileTap={{ scale: 0.95 }}
                        onMouseEnter={() => playFeedback('buttonHover')}
                        onClick={() => playFeedback('click')}
                        className="w-full sm:w-auto btn-secondary text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 border-2 border-white text-white hover:bg-white hover:text-blue-600 min-h-[44px] transition-all duration-300"
                      >
                        Explore Modules
                      </motion.button>
                    </Link>
                  </motion.div>
                </>
              ) : (
                <>
                  <motion.div
                    initial={{ opacity: 0, y: 30, scale: 0.8 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: 0.6,
                      delay: 3.2,
                      type: "spring",
                      stiffness: 150
                    }}
                  >
                    <motion.button
                      whileHover={{
                        scale: 1.05,
                        y: -4,
                        boxShadow: "0 15px 35px rgba(0,0,0,0.2)",
                        rotate: [0, 1, -1, 0]
                      }}
                      whileTap={{ scale: 0.95 }}
                      onMouseEnter={() => playFeedback('buttonHover')}
                      onClick={() => {
                        setShowAuthModal(true)
                        playFeedback('click')
                        setShowCelebration(true)
                      }}
                      className="w-full sm:w-auto btn-primary text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 bg-white text-blue-600 hover:bg-gray-100 min-h-[44px] flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                    >
                      <motion.div
                        animate={{
                          scale: [1, 1.2, 1],
                          rotate: [0, 180, 360]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        <Star className="w-5 h-5" />
                      </motion.div>
                      <span>🚀 Start Your Quest</span>

                      {/* Shimmer Effect */}
                      <motion.div
                        initial={{ x: "-100%" }}
                        animate={{ x: "100%" }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          repeatDelay: 3,
                          ease: "linear"
                        }}
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                      />
                    </motion.button>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 30, scale: 0.8 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: 0.6,
                      delay: 3.5,
                      type: "spring",
                      stiffness: 150
                    }}
                  >
                    <motion.button
                      whileHover={{
                        scale: 1.05,
                        backgroundColor: "rgba(255,255,255,0.1)",
                        borderColor: "rgba(255,255,255,0.8)"
                      }}
                      whileTap={{ scale: 0.95 }}
                      onMouseEnter={() => playFeedback('buttonHover')}
                      onClick={() => {
                        setShowAuthModal(true)
                        playFeedback('click')
                      }}
                      className="w-full sm:w-auto btn-secondary text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 border-2 border-white text-white hover:bg-white hover:text-blue-600 min-h-[44px] flex items-center justify-center space-x-2 transition-all duration-300"
                    >
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <Lock className="w-4 h-4" />
                      </motion.div>
                      <span>Preview Modules</span>
                    </motion.button>
                  </motion.div>
                </>
              )}
            </motion.div>
          </motion.div>
        </div>
        
        {/* Floating Elements - Hidden on mobile */}
        <div className="hidden md:block absolute top-20 left-10 floating-animation">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
            <BookOpen className="w-8 h-8" />
          </div>
        </div>
        <div className="hidden md:block absolute top-40 right-20 floating-animation-delay-1">
          <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
            <Trophy className="w-6 h-6" />
          </div>
        </div>
        <div className="hidden md:block absolute bottom-20 left-1/4 floating-animation-delay-2">
          <div className="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center">
            <Target className="w-7 h-7" />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 sm:py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center p-4 sm:p-6 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 hover:shadow-lg transition-all duration-300"
              >
                <stat.icon className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 sm:mb-3 text-primary-600" />
                <div className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-1">{stat.value}</div>
                <div className="text-gray-600 text-xs sm:text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Progress Overview */}
      <ProgressOverview />

      {/* Premium Value Proposition Section */}
      <section className="py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 text-white relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-32 h-32 bg-yellow-400 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-pink-400 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-blue-400 rounded-full blur-3xl animate-pulse delay-2000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="inline-block mb-6"
            >
              <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto">
                <Star className="w-10 h-10 text-white" />
              </div>
            </motion.div>
            <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
              Premium Learning Experience
            </h2>
            <p className="text-xl md:text-2xl text-purple-100 max-w-4xl mx-auto leading-relaxed">
              Discover what makes our platform the ultimate destination for social media marketing mastery
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Audio Experience */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mb-6">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  🎵
                </motion.div>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-yellow-300">Immersive Audio Experience</h3>
              <p className="text-purple-100 mb-4">
                Enjoy premium background music and interactive sound effects that enhance your learning journey.
                Our carefully curated audio creates a dopamine-driven experience that keeps you engaged and motivated.
              </p>
              <ul className="text-sm text-purple-200 space-y-2">
                <li>• Ambient background music for focus</li>
                <li>• Achievement celebration sounds</li>
                <li>• Interactive feedback audio</li>
                <li>• Customizable volume controls</li>
              </ul>
            </motion.div>

            {/* Comprehensive Content */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center mb-6">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-yellow-300">Comprehensive Learning Modules</h3>
              <p className="text-purple-100 mb-4">
                Master every aspect of social media marketing with our expertly crafted curriculum covering
                Instagram, Facebook, Google AdWords, and advanced strategies.
              </p>
              <ul className="text-sm text-purple-200 space-y-2">
                <li>• 50+ interactive chapters</li>
                <li>• Real-world case studies</li>
                <li>• Practical exercises & quizzes</li>
                <li>• Industry best practices</li>
              </ul>
            </motion.div>

            {/* Gamified Progress */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center mb-6">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-yellow-300">Gamified Achievement System</h3>
              <p className="text-purple-100 mb-4">
                Unlock achievements, earn XP, and track your progress with our advanced gamification system
                designed to maximize motivation and retention.
              </p>
              <ul className="text-sm text-purple-200 space-y-2">
                <li>• XP points and level progression</li>
                <li>• Achievement badges & rewards</li>
                <li>• Progress tracking & analytics</li>
                <li>• Celebration animations</li>
              </ul>
            </motion.div>

            {/* Cross-Device Sync */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center mb-6">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-yellow-300">Cloud-Powered Progress</h3>
              <p className="text-purple-100 mb-4">
                Your progress is automatically saved to the cloud, allowing seamless learning across all your devices
                with real-time synchronization.
              </p>
              <ul className="text-sm text-purple-200 space-y-2">
                <li>• Automatic cloud backup</li>
                <li>• Cross-device synchronization</li>
                <li>• Offline learning capability</li>
                <li>• Never lose your progress</li>
              </ul>
            </motion.div>

            {/* Interactive Learning */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center mb-6">
                <Target className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-yellow-300">Interactive Learning Tools</h3>
              <p className="text-purple-100 mb-4">
                Engage with dynamic content including drag-and-drop exercises, interactive quizzes,
                and hands-on activities that reinforce learning.
              </p>
              <ul className="text-sm text-purple-200 space-y-2">
                <li>• Interactive exercises</li>
                <li>• Dynamic content adaptation</li>
                <li>• Immediate feedback</li>
                <li>• Personalized learning paths</li>
              </ul>
            </motion.div>

            {/* Premium Support */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mb-6">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-yellow-300">Expert-Crafted Content</h3>
              <p className="text-purple-100 mb-4">
                Learn from industry experts with content that's constantly updated to reflect the latest
                social media marketing trends and best practices.
              </p>
              <ul className="text-sm text-purple-200 space-y-2">
                <li>• Industry expert insights</li>
                <li>• Regular content updates</li>
                <li>• Current market trends</li>
                <li>• Proven strategies & tactics</li>
              </ul>
            </motion.div>
          </div>

          {/* Value Highlight */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="text-center bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-3xl p-12 border border-yellow-400/30"
          >
            <h3 className="text-4xl font-bold mb-6 text-yellow-300">
              🎯 Transform Your Career in Social Media Marketing
            </h3>
            <p className="text-xl text-purple-100 mb-8 max-w-4xl mx-auto">
              Join thousands of successful marketers who have mastered Instagram growth, Facebook advertising,
              Google AdWords optimization, and advanced social media strategies through our premium platform.
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-lg">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                <span>Premium Audio Experience</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                <span>Cloud Progress Sync</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                <span>Expert-Level Content</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                <span>Gamified Learning</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Learning Modules */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Learning Modules</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Four comprehensive modules designed to take you from beginner to expert in social media marketing.
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ModuleCard module={module} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements Showcase */}
      <AchievementShowcase />

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold mb-6">Ready to Master Social Media Marketing?</h2>
            <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
              Join thousands of learners who have transformed their marketing skills through our gamified platform.
            </p>
            {authState.isAuthenticated ? (
              <Link href="/modules">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onMouseEnter={() => playFeedback('buttonHover')}
                  onClick={() => playFeedback('click')}
                  className="btn-primary text-lg px-8 py-4 bg-white text-primary-600 hover:bg-gray-100 min-h-[44px]"
                >
                  Begin Your Journey
                </motion.button>
              </Link>
            ) : (
              <div className="flex justify-center">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  onMouseEnter={() => playFeedback('buttonHover')}
                  onClick={() => {
                    setShowAuthModal(true)
                    playFeedback('click')
                  }}
                  className="btn-primary text-lg px-8 py-4 bg-white text-primary-600 hover:bg-gray-100 min-h-[44px] flex items-center space-x-2 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Star className="w-5 h-5" />
                  <span>🎯 Join the Quest</span>
                </motion.button>
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialMode="signup"
      />

      {/* Celebration Particles */}
      <ParticleSystem
        trigger={showCelebration}
        type="celebration"
        count={30}
        onComplete={() => setShowCelebration(false)}
      />
    </div>
  )
}
