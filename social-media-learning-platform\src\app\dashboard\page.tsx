'use client'

import { motion } from 'framer-motion'
import { 
  Trophy, 
  Target, 
  BookOpen, 
  Star, 
  TrendingUp, 
  Calendar,
  Clock,
  Award,
  Play,
  CheckCircle
} from 'lucide-react'
import Link from 'next/link'
import { useGame } from '@/contexts/GameContext'
import { useAudio } from '@/hooks/useAudio'
import { AuthGuard } from '@/components/Auth/AuthGuard'
import { ParticleSystem } from '@/components/Animations/ParticleSystem'
import { useState, useEffect } from 'react'

export default function DashboardPage() {
  const { gameState } = useGame()
  const { playFeedback } = useAudio()
  const [showCelebration, setShowCelebration] = useState(false)

  const totalModules = 4
  const totalChapters = 56
  const completionPercentage = (gameState.completedModules.length / totalModules) * 100
  const nextLevelXP = gameState.level * 1000
  const currentLevelXP = (gameState.level - 1) * 1000
  const progressToNextLevel = ((gameState.totalXP - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100

  const modules = [
    { id: 'foundations', title: 'Social Media Foundations', chapters: 12, color: 'from-blue-500 to-cyan-500' },
    { id: 'instagram', title: 'Instagram Mastery', chapters: 15, color: 'from-pink-500 to-purple-500' },
    { id: 'facebook', title: 'Facebook Advertising Pro', chapters: 15, color: 'from-blue-600 to-indigo-600' },
    { id: 'google', title: 'Google AdWords Expert', chapters: 14, color: 'from-green-500 to-emerald-500' }
  ]

  const recentAchievements = gameState.achievements
    .sort((a, b) => new Date(b.unlockedAt || 0).getTime() - new Date(a.unlockedAt || 0).getTime())
    .slice(0, 3)

  const getNextModule = () => {
    return modules.find(m => !gameState.completedModules.includes(m.id))
  }

  const nextModule = getNextModule()

  return (
    <AuthGuard requireAuth={true}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <section className="py-8 sm:py-12 bg-gradient-to-r from-primary-600 to-accent-600 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-2 sm:mb-4">
              Welcome Back, Learner! 👋
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-blue-100">
              Continue your social media mastery journey and track your progress.
            </p>
          </motion.div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-6 sm:py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
          {[
            { label: 'Total XP', value: gameState.totalXP, icon: Star, color: 'from-yellow-500 to-orange-500' },
            { label: 'Level', value: gameState.level, icon: Trophy, color: 'from-purple-500 to-pink-500' },
            { label: 'Modules', value: `${gameState.completedModules.length}/${totalModules}`, icon: BookOpen, color: 'from-blue-500 to-cyan-500' },
            { label: 'Achievements', value: gameState.achievements.length, icon: Award, color: 'from-green-500 to-emerald-500' }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white rounded-xl p-4 sm:p-6 shadow-lg"
            >
              <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-gradient-to-r ${stat.color} flex items-center justify-center mb-2 sm:mb-3`}>
                <stat.icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-lg sm:text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
              <div className="text-gray-600 text-xs sm:text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </div>

        <div className="grid lg:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6 sm:space-y-8">
            {/* Level Progress */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-xl p-4 sm:p-6 shadow-lg"
            >
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Level Progress</h2>
                <div className="flex items-center space-x-2 text-primary-600">
                  <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="font-semibold text-sm sm:text-base">Level {gameState.level}</span>
                </div>
              </div>
              
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>{gameState.totalXP} XP</span>
                  <span>{nextLevelXP - gameState.totalXP} XP to next level</span>
                </div>
                <div className="progress-bar h-3">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${progressToNextLevel}%` }}
                    transition={{ duration: 1.5, delay: 0.5 }}
                    className="h-full bg-gradient-to-r from-primary-400 to-primary-600 rounded-full"
                  />
                </div>
              </div>
              
              <p className="text-gray-600">
                Keep learning to reach Level {gameState.level + 1}! Complete chapters and modules to earn more XP.
              </p>
            </motion.div>

            {/* Module Progress */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-xl p-6 shadow-lg"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Module Progress</h2>
              
              <div className="space-y-4">
                {modules.map((module, index) => {
                  const progress = gameState.moduleProgress[module.id] || 0
                  const isCompleted = gameState.completedModules.includes(module.id)
                  const progressPercentage = (progress / module.chapters) * 100
                  
                  return (
                    <div key={module.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold text-gray-900">{module.title}</h3>
                        <div className="flex items-center space-x-2">
                          {isCompleted && <CheckCircle className="w-5 h-5 text-green-500" />}
                          <span className="text-sm text-gray-600">
                            {progress}/{module.chapters}
                          </span>
                        </div>
                      </div>
                      
                      <div className="progress-bar mb-3">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${progressPercentage}%` }}
                          transition={{ duration: 1, delay: index * 0.2 }}
                          className={`h-full bg-gradient-to-r ${module.color} rounded-full`}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">
                          {Math.round(progressPercentage)}% complete
                        </span>
                        <Link href={`/modules/${module.id}`}>
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onMouseEnter={() => playFeedback('buttonHover')}
                            onClick={() => playFeedback('click')}
                            className="text-sm px-3 py-1 bg-primary-100 text-primary-700 rounded-lg hover:bg-primary-200 transition-colors"
                          >
                            {isCompleted ? 'Review' : progress > 0 ? 'Continue' : 'Start'}
                          </motion.button>
                        </Link>
                      </div>
                    </div>
                  )
                })}
              </div>
            </motion.div>

            {/* Continue Learning */}
            {nextModule && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-gradient-to-r from-primary-500 to-accent-500 rounded-xl p-6 text-white"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Continue Learning</h2>
                    <p className="text-blue-100 mb-4">
                      Ready to tackle the next module in your learning journey?
                    </p>
                    <div className="text-lg font-semibold">{nextModule.title}</div>
                  </div>
                  <Link href={`/modules/${nextModule.id}`}>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                    >
                      <Play className="w-5 h-5" />
                      <span>Start Module</span>
                    </motion.button>
                  </Link>
                </div>
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Achievements */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-xl p-6 shadow-lg"
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">Recent Achievements</h3>
              
              {recentAchievements.length > 0 ? (
                <div className="space-y-3">
                  {recentAchievements.map((achievement, index) => (
                    <motion.div
                      key={achievement.id}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200"
                    >
                      <div className="text-2xl">{achievement.icon}</div>
                      <div>
                        <div className="font-semibold text-gray-900">{achievement.title}</div>
                        <div className="text-sm text-gray-600">{achievement.description}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Trophy className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No achievements yet. Start learning to unlock your first badge!</p>
                </div>
              )}
              
              <Link href="/achievements">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full mt-4 py-2 text-center text-primary-600 hover:text-primary-700 font-medium"
                >
                  View All Achievements
                </motion.button>
              </Link>
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white rounded-xl p-6 shadow-lg"
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">Quick Actions</h3>
              
              <div className="space-y-3">
                <Link href="/modules">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <BookOpen className="w-5 h-5 text-primary-600" />
                    <span>Browse All Modules</span>
                  </motion.button>
                </Link>
                
                <Link href="/achievements">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Trophy className="w-5 h-5 text-yellow-600" />
                    <span>View Achievements</span>
                  </motion.button>
                </Link>
                
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors"
                  onClick={() => {
                    // Reset progress (for demo purposes)
                    if (confirm('Are you sure you want to reset your progress? This action cannot be undone.')) {
                      localStorage.removeItem('socialMediaGameState')
                      window.location.reload()
                    }
                  }}
                >
                  <Target className="w-5 h-5 text-red-600" />
                  <span>Reset Progress</span>
                </motion.button>
              </div>
            </motion.div>

            {/* Learning Stats */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white rounded-xl p-6 shadow-lg"
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">Learning Stats</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Chapters Completed</span>
                  <span className="font-semibold">{gameState.completedChapters.length}/{totalChapters}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Overall Progress</span>
                  <span className="font-semibold">{Math.round(completionPercentage)}%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Current Streak</span>
                  <span className="font-semibold">{gameState.streakDays} days</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Last Active</span>
                  <span className="font-semibold text-sm">
                    {gameState.lastActiveDate 
                      ? new Date(gameState.lastActiveDate).toLocaleDateString()
                      : 'Never'
                    }
                  </span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Celebration Particles */}
      <ParticleSystem
        trigger={showCelebration}
        type="celebration"
        count={25}
        onComplete={() => setShowCelebration(false)}
      />
      </div>
    </AuthGuard>
  )
}
