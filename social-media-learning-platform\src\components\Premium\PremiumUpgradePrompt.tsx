'use client'

import { motion } from 'framer-motion'
import { Star, Trophy, Zap, Lock, Crown, CheckCircle } from 'lucide-react'
import { useState } from 'react'
import { AuthModal } from '@/components/Auth/AuthModal'
import { useAudio } from '@/hooks/useAudio'

export function PremiumUpgradePrompt() {
  const [showAuthModal, setShowAuthModal] = useState(false)
  const { playFeedback } = useAudio()

  const premiumFeatures = [
    {
      icon: Trophy,
      title: 'Complete Achievement System',
      description: 'Unlock all badges, track your progress, and showcase your expertise'
    },
    {
      icon: Zap,
      title: 'Premium Audio Experience',
      description: 'Immersive background music and interactive sound effects'
    },
    {
      icon: Star,
      title: 'Full Module Access',
      description: 'Access all 4 comprehensive social media marketing modules'
    },
    {
      icon: Crown,
      title: 'Expert-Level Content',
      description: 'Advanced strategies, case studies, and industry insights'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 w-32 h-32 bg-yellow-400 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-pink-400 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-blue-400 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-16"
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="inline-block mb-6"
            >
              <div className="w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto shadow-2xl">
                <Lock className="w-12 h-12 text-white" />
              </div>
            </motion.div>
            
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
              Premium Content Awaits
            </h1>
            
            <p className="text-xl md:text-2xl text-purple-100 max-w-3xl mx-auto leading-relaxed">
              Unlock the complete Social Media Mastery Quest experience with premium features, 
              comprehensive content, and advanced learning tools.
            </p>
          </motion.div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {premiumFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-300">{feature.title}</h3>
                <p className="text-purple-100">{feature.description}</p>
              </motion.div>
            ))}
          </div>

          {/* What You Get */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-3xl p-12 border border-yellow-400/30 mb-16"
          >
            <h2 className="text-4xl font-bold mb-8 text-yellow-300">
              🎯 What You'll Master
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6 text-left">
              {[
                'Instagram Growth & Engagement Strategies',
                'Facebook Advertising & Audience Targeting',
                'Google AdWords & Search Marketing',
                'Advanced Social Media Analytics',
                'Content Creation & Viral Marketing',
                'Influencer Collaboration Techniques',
                'Brand Building & Community Management',
                'ROI Optimization & Performance Tracking'
              ].map((skill, index) => (
                <motion.div
                  key={skill}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0" />
                  <span className="text-purple-100">{skill}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-center"
          >
            <h3 className="text-3xl font-bold mb-6 text-white">
              Ready to Transform Your Social Media Marketing Skills?
            </h3>
            
            <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
              Join thousands of successful marketers who have mastered social media through our premium platform.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onMouseEnter={() => playFeedback('buttonHover')}
                onClick={() => {
                  setShowAuthModal(true)
                  playFeedback('click')
                }}
                className="px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 rounded-xl font-bold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center justify-center space-x-2"
              >
                <Crown className="w-6 h-6" />
                <span>🚀 Start Premium Quest</span>
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onMouseEnter={() => playFeedback('buttonHover')}
                onClick={() => {
                  window.location.href = '/'
                  playFeedback('click')
                }}
                className="px-8 py-4 border-2 border-white text-white rounded-xl font-bold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300"
              >
                Back to Home
              </motion.button>
            </div>

            <p className="text-sm text-purple-200 mt-6">
              ✨ Premium features include cloud sync, offline access, and priority support
            </p>
          </motion.div>
        </div>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialMode="signup"
      />
    </div>
  )
}
