'use client'

import { useCallback, useRef, useEffect, useState, useMemo } from 'react'

type SoundType =
  | 'click' | 'success' | 'error' | 'achievement' | 'levelUp' | 'notification'
  | 'chapterComplete' | 'moduleComplete' | 'streakBonus' | 'coinCollect'
  | 'pageTransition' | 'buttonHover' | 'modalOpen' | 'modalClose'
  | 'progressTick' | 'unlock' | 'celebration' | 'ambient'

interface AudioSettings {
  enabled: boolean
  volume: number
  ambientEnabled: boolean
  ambientVolume: number
  backgroundMusicEnabled: boolean
  backgroundMusicVolume: number
}

export const useAudio = () => {
  const audioContextRef = useRef<AudioContext | null>(null)
  const fadeTimeouts = useRef<{ [key: string]: NodeJS.Timeout }>({})
  const backgroundMusicRef = useRef<HTMLAudioElement | null>(null)
  const [isBackgroundMusicPlaying, setIsBackgroundMusicPlaying] = useState(false)
  const [settings, setSettings] = useState<AudioSettings>({
    enabled: true,
    volume: 0.7,
    ambientEnabled: false,
    ambientVolume: 0.3,
    backgroundMusicEnabled: false,
    backgroundMusicVolume: 0.4
  })

  // Initialize audio context
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // Create audio context on first user interaction
        const initAudioContext = () => {
          if (!audioContextRef.current) {
            audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()

            // Resume context if suspended
            if (audioContextRef.current.state === 'suspended') {
              audioContextRef.current.resume()
            }
          }
        }

        // Initialize background music
        const initBackgroundMusic = () => {
          if (!backgroundMusicRef.current) {
            backgroundMusicRef.current = new Audio('/audio/Chill Lofi Mix [chill lo-fi hip hop beats].mp3')
            backgroundMusicRef.current.loop = true
            backgroundMusicRef.current.volume = 0.4
            backgroundMusicRef.current.preload = 'auto'

            // Add event listeners to track play/pause state
            backgroundMusicRef.current.addEventListener('play', () => {
              setIsBackgroundMusicPlaying(true)
            })

            backgroundMusicRef.current.addEventListener('pause', () => {
              setIsBackgroundMusicPlaying(false)
            })

            backgroundMusicRef.current.addEventListener('ended', () => {
              setIsBackgroundMusicPlaying(false)
            })

            // Handle audio loading errors gracefully
            backgroundMusicRef.current.addEventListener('error', (e) => {
              console.warn('Background music failed to load:', e)
              setIsBackgroundMusicPlaying(false)
            })
          }
        }

        // Add event listeners for user interaction
        const events = ['click', 'touchstart', 'keydown']
        const handleFirstInteraction = () => {
          initAudioContext()
          initBackgroundMusic()
          // Note: No auto-start - user must explicitly enable background music
        }

        events.forEach(event => {
          document.addEventListener(event, handleFirstInteraction, { once: true })
        })

        // Load settings from localStorage
        const savedSettings = localStorage.getItem('audioSettings')
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings)
          setSettings({
            enabled: true,
            volume: 0.7,
            ambientEnabled: false,
            ambientVolume: 0.3,
            backgroundMusicEnabled: false,
            backgroundMusicVolume: 0.4,
            ...parsed
          })
        }

        return () => {
          events.forEach(event => {
            document.removeEventListener(event, handleFirstInteraction)
          })
        }
      } catch (error) {
        console.warn('Audio context not supported:', error)
      }
    }
  }, [])

  // Save settings to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('audioSettings', JSON.stringify(settings))
    }
  }, [settings])

  // Enhanced audio creation with effects
  const createTone = useCallback((
    frequency: number,
    duration: number,
    type: OscillatorType = 'sine',
    options: {
      volume?: number
      attack?: number
      decay?: number
      sustain?: number
      release?: number
      filter?: { frequency: number; type: BiquadFilterType }
    } = {}
  ) => {
    if (!audioContextRef.current || !settings.enabled) return

    try {
      if (audioContextRef.current.state === 'suspended') {
        audioContextRef.current.resume()
      }

      const oscillator = audioContextRef.current.createOscillator()
      const gainNode = audioContextRef.current.createGain()
      const filterNode = audioContextRef.current.createBiquadFilter()

      // Connect nodes
      oscillator.connect(filterNode)
      filterNode.connect(gainNode)
      gainNode.connect(audioContextRef.current.destination)

      // Configure oscillator
      oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime)
      oscillator.type = type

      // Configure filter if provided
      if (options.filter) {
        filterNode.type = options.filter.type
        filterNode.frequency.setValueAtTime(options.filter.frequency, audioContextRef.current.currentTime)
      }

      // ADSR envelope
      const volume = options.volume ?? settings.volume * 0.3
      const attack = options.attack ?? 0.01
      const decay = options.decay ?? 0.1
      const sustain = options.sustain ?? 0.7
      const release = options.release ?? 0.3

      const now = audioContextRef.current.currentTime
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(volume, now + attack)
      gainNode.gain.linearRampToValueAtTime(volume * sustain, now + attack + decay)
      gainNode.gain.setValueAtTime(volume * sustain, now + duration - release)
      gainNode.gain.exponentialRampToValueAtTime(0.01, now + duration)

      oscillator.start(now)
      oscillator.stop(now + duration)

      oscillator.onended = () => {
        oscillator.disconnect()
        filterNode.disconnect()
        gainNode.disconnect()
      }
    } catch (error) {
      console.warn('Audio playback failed:', error)
    }
  }, [settings])

  // Enhanced sound library
  const soundLibrary = useMemo(() => ({
    click: () => createTone(800, 0.08, 'square', {
      attack: 0.005, decay: 0.02, sustain: 0.3, release: 0.05,
      filter: { frequency: 2000, type: 'lowpass' }
    }),

    buttonHover: () => createTone(600, 0.05, 'sine', {
      volume: settings.volume * 0.1, attack: 0.01, release: 0.04
    }),

    success: () => {
      // Triumphant chord
      createTone(523.25, 0.4, 'sine', { attack: 0.02, sustain: 0.8 }) // C5
      setTimeout(() => createTone(659.25, 0.4, 'sine', { attack: 0.02, sustain: 0.8 }), 50) // E5
      setTimeout(() => createTone(783.99, 0.6, 'sine', { attack: 0.02, sustain: 0.8 }), 100) // G5
    },

    error: () => createTone(220, 0.4, 'sawtooth', {
      attack: 0.01, decay: 0.1, sustain: 0.5, release: 0.3,
      filter: { frequency: 800, type: 'lowpass' }
    }),

    achievement: () => {
      // Epic fanfare
      const chord1 = [261.63, 329.63, 392.00] // C major
      const chord2 = [293.66, 369.99, 440.00] // D major
      const chord3 = [329.63, 415.30, 493.88] // E major

      chord1.forEach(freq => createTone(freq, 0.3, 'sine', { attack: 0.02, sustain: 0.9 }))
      setTimeout(() => {
        chord2.forEach(freq => createTone(freq, 0.3, 'sine', { attack: 0.02, sustain: 0.9 }))
      }, 200)
      setTimeout(() => {
        chord3.forEach(freq => createTone(freq, 0.5, 'sine', { attack: 0.02, sustain: 0.9 }))
      }, 400)
    },

    levelUp: () => {
      // Magical ascending arpeggio
      const notes = [261.63, 329.63, 392.00, 523.25, 659.25, 783.99, 1046.50]
      notes.forEach((note, index) => {
        setTimeout(() => createTone(note, 0.2, 'triangle', {
          attack: 0.01, decay: 0.05, sustain: 0.7, release: 0.15
        }), index * 60)
      })
    },

    chapterComplete: () => {
      createTone(440, 0.15, 'sine')
      setTimeout(() => createTone(554.37, 0.15, 'sine'), 100)
      setTimeout(() => createTone(659.25, 0.25, 'sine'), 200)
    },

    moduleComplete: () => {
      // Victory fanfare
      const melody = [523.25, 659.25, 783.99, 1046.50, 783.99, 1046.50]
      melody.forEach((note, index) => {
        setTimeout(() => createTone(note, 0.2, 'triangle', {
          attack: 0.01, sustain: 0.8
        }), index * 120)
      })
    },

    streakBonus: () => {
      // Sparkling sound
      for (let i = 0; i < 5; i++) {
        setTimeout(() => {
          createTone(800 + Math.random() * 400, 0.1, 'sine', {
            volume: settings.volume * 0.2, attack: 0.01, release: 0.09
          })
        }, i * 50)
      }
    },

    coinCollect: () => createTone(1000, 0.1, 'square', {
      attack: 0.01, decay: 0.02, sustain: 0.3, release: 0.07,
      filter: { frequency: 3000, type: 'lowpass' }
    }),

    unlock: () => {
      createTone(523.25, 0.1, 'sine')
      setTimeout(() => createTone(659.25, 0.1, 'sine'), 80)
      setTimeout(() => createTone(783.99, 0.2, 'sine'), 160)
    },

    celebration: () => {
      // Party sound
      for (let i = 0; i < 8; i++) {
        setTimeout(() => {
          createTone(400 + Math.random() * 600, 0.15, 'triangle', {
            volume: settings.volume * 0.25
          })
        }, i * 80)
      }
    },

    notification: () => {
      createTone(880, 0.12, 'sine', { attack: 0.01, release: 0.11 })
      setTimeout(() => createTone(1108.73, 0.12, 'sine', { attack: 0.01, release: 0.11 }), 120)
    },

    modalOpen: () => createTone(440, 0.15, 'sine', {
      attack: 0.02, decay: 0.05, sustain: 0.6, release: 0.08
    }),

    modalClose: () => createTone(330, 0.12, 'sine', {
      attack: 0.01, decay: 0.03, sustain: 0.4, release: 0.08
    }),

    pageTransition: () => createTone(523.25, 0.2, 'triangle', {
      attack: 0.05, decay: 0.05, sustain: 0.5, release: 0.1
    }),

    progressTick: () => createTone(660, 0.08, 'triangle', {
      volume: settings.volume * 0.15, attack: 0.01, release: 0.07
    }),

    ambient: () => {
      // Gentle ambient drone (for background)
      createTone(110, 8, 'sine', {
        volume: settings.ambientVolume * 0.1, attack: 2, release: 2
      })
      setTimeout(() => {
        createTone(165, 8, 'sine', {
          volume: settings.ambientVolume * 0.08, attack: 2, release: 2
        })
      }, 1000)
    }
  }), [createTone, settings])

  // Haptic feedback for mobile
  const triggerHaptic = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (typeof window !== 'undefined' && 'navigator' in window && 'vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30]
      }
      navigator.vibrate(patterns[type])
    }
  }, [])

  // Enhanced playFeedback with haptic integration
  const playFeedback = useCallback((type: SoundType) => {
    if (!settings.enabled && type !== 'ambient') return
    if (type === 'ambient' && !settings.ambientEnabled) return

    // Play sound
    const soundFunction = soundLibrary[type]
    if (soundFunction) {
      soundFunction()
    }

    // Add haptic feedback for mobile
    const hapticMap: { [key in SoundType]?: 'light' | 'medium' | 'heavy' } = {
      click: 'light',
      buttonHover: 'light',
      success: 'medium',
      error: 'heavy',
      achievement: 'heavy',
      levelUp: 'heavy',
      chapterComplete: 'medium',
      moduleComplete: 'heavy',
      streakBonus: 'medium',
      coinCollect: 'light',
      unlock: 'medium',
      celebration: 'heavy',
      notification: 'medium',
      modalOpen: 'light',
      modalClose: 'light',
      progressTick: 'light'
    }

    const hapticType = hapticMap[type]
    if (hapticType) {
      triggerHaptic(hapticType)
    }
  }, [settings, soundLibrary, triggerHaptic])

  // Play sequence of sounds
  const playSequence = useCallback((sounds: SoundType[], delay: number = 200) => {
    sounds.forEach((sound, index) => {
      setTimeout(() => playFeedback(sound), index * delay)
    })
  }, [playFeedback])

  // Stop all audio
  const stopAll = useCallback(() => {
    if (audioContextRef.current) {
      audioContextRef.current.close()
      audioContextRef.current = null
    }
  }, [])

  // Background music controls
  const playBackgroundMusic = useCallback(() => {
    if (backgroundMusicRef.current && settings.backgroundMusicEnabled) {
      backgroundMusicRef.current.play().catch((error) => {
        console.warn('Failed to play background music:', error)
        setIsBackgroundMusicPlaying(false)
      })
    }
  }, [settings.backgroundMusicEnabled])

  const pauseBackgroundMusic = useCallback(() => {
    if (backgroundMusicRef.current) {
      backgroundMusicRef.current.pause()
    }
  }, [])

  const toggleBackgroundMusic = useCallback(() => {
    if (!backgroundMusicRef.current) return

    if (backgroundMusicRef.current.paused) {
      playBackgroundMusic()
    } else {
      pauseBackgroundMusic()
    }
  }, [playBackgroundMusic, pauseBackgroundMusic])

  // Update background music volume and state when settings change
  useEffect(() => {
    if (backgroundMusicRef.current) {
      backgroundMusicRef.current.volume = settings.backgroundMusicVolume

      if (settings.backgroundMusicEnabled && backgroundMusicRef.current.paused) {
        playBackgroundMusic()
      } else if (!settings.backgroundMusicEnabled && !backgroundMusicRef.current.paused) {
        pauseBackgroundMusic()
      }
    }
  }, [settings.backgroundMusicEnabled, settings.backgroundMusicVolume, playBackgroundMusic, pauseBackgroundMusic])

  // Update settings with validation
  const updateSettings = useCallback((newSettings: Partial<AudioSettings>) => {
    setSettings(prev => {
      const updatedSettings = {
        ...prev,
        ...newSettings,
        volume: Math.max(0, Math.min(1, newSettings.volume ?? prev.volume)),
        ambientVolume: Math.max(0, Math.min(1, newSettings.ambientVolume ?? prev.ambientVolume)),
        backgroundMusicVolume: Math.max(0, Math.min(1, newSettings.backgroundMusicVolume ?? prev.backgroundMusicVolume))
      }

      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('audioSettings', JSON.stringify(updatedSettings))
      }

      return updatedSettings
    })
  }, [])

  return {
    settings,
    updateSettings,
    playFeedback,
    playSequence,
    stopAll,
    triggerHaptic,
    playBackgroundMusic,
    pauseBackgroundMusic,
    toggleBackgroundMusic,
    isBackgroundMusicPlaying
  }
}
