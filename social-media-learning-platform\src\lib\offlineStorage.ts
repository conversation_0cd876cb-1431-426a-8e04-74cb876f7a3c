'use client'

interface OfflineProgress {
  userId: string
  completedChapters: string[]
  totalXP: number
  level: number
  completedModules: string[]
  quizScores: Record<string, number>
  timeSpent: number
  streakDays: number
  lastActiveDate: string
  timestamp: number
}

interface PendingAction {
  id: string
  type: 'COMPLETE_CHAPTER' | 'COMPLETE_MODULE' | 'UPDATE_QUIZ_SCORE'
  payload: any
  timestamp: number
  retryCount: number
}

class OfflineStorageService {
  private readonly PROGRESS_KEY = 'smlp_offline_progress'
  private readonly PENDING_ACTIONS_KEY = 'smlp_pending_actions'
  private readonly MAX_RETRY_COUNT = 5

  // Save progress to localStorage
  saveProgress(progress: Partial<OfflineProgress>): void {
    try {
      const existingProgress = this.getProgress()
      const updatedProgress = {
        ...existingProgress,
        ...progress,
        timestamp: Date.now()
      }
      
      localStorage.setItem(this.PROGRESS_KEY, JSON.stringify(updatedProgress))
    } catch (error) {
      console.error('Failed to save offline progress:', error)
    }
  }

  // Get progress from localStorage
  getProgress(): OfflineProgress | null {
    try {
      const stored = localStorage.getItem(this.PROGRESS_KEY)
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.error('Failed to get offline progress:', error)
      return null
    }
  }

  // Clear stored progress
  clearProgress(): void {
    try {
      localStorage.removeItem(this.PROGRESS_KEY)
    } catch (error) {
      console.error('Failed to clear offline progress:', error)
    }
  }

  // Add pending action for when back online
  addPendingAction(action: Omit<PendingAction, 'id' | 'timestamp' | 'retryCount'>): void {
    try {
      const pendingActions = this.getPendingActions()
      const newAction: PendingAction = {
        ...action,
        id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        retryCount: 0
      }
      
      pendingActions.push(newAction)
      localStorage.setItem(this.PENDING_ACTIONS_KEY, JSON.stringify(pendingActions))
    } catch (error) {
      console.error('Failed to add pending action:', error)
    }
  }

  // Get all pending actions
  getPendingActions(): PendingAction[] {
    try {
      const stored = localStorage.getItem(this.PENDING_ACTIONS_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Failed to get pending actions:', error)
      return []
    }
  }

  // Remove completed action
  removePendingAction(actionId: string): void {
    try {
      const pendingActions = this.getPendingActions()
      const filteredActions = pendingActions.filter(action => action.id !== actionId)
      localStorage.setItem(this.PENDING_ACTIONS_KEY, JSON.stringify(filteredActions))
    } catch (error) {
      console.error('Failed to remove pending action:', error)
    }
  }

  // Increment retry count for failed action
  incrementRetryCount(actionId: string): void {
    try {
      const pendingActions = this.getPendingActions()
      const updatedActions = pendingActions.map(action => 
        action.id === actionId 
          ? { ...action, retryCount: action.retryCount + 1 }
          : action
      )
      localStorage.setItem(this.PENDING_ACTIONS_KEY, JSON.stringify(updatedActions))
    } catch (error) {
      console.error('Failed to increment retry count:', error)
    }
  }

  // Remove actions that have exceeded max retry count
  cleanupFailedActions(): void {
    try {
      const pendingActions = this.getPendingActions()
      const validActions = pendingActions.filter(action => action.retryCount < this.MAX_RETRY_COUNT)
      localStorage.setItem(this.PENDING_ACTIONS_KEY, JSON.stringify(validActions))
    } catch (error) {
      console.error('Failed to cleanup failed actions:', error)
    }
  }

  // Sync pending actions when back online
  async syncPendingActions(
    onCompleteChapter: (moduleId: string, chapterId: string, xp: number) => Promise<void>,
    onCompleteModule: (moduleId: string, xp: number) => Promise<void>,
    onUpdateQuizScore: (quizId: string, score: number) => Promise<void>
  ): Promise<{ success: number; failed: number }> {
    const pendingActions = this.getPendingActions()
    let successCount = 0
    let failedCount = 0

    for (const action of pendingActions) {
      try {
        switch (action.type) {
          case 'COMPLETE_CHAPTER':
            await onCompleteChapter(
              action.payload.moduleId,
              action.payload.chapterId,
              action.payload.xp
            )
            break
          case 'COMPLETE_MODULE':
            await onCompleteModule(
              action.payload.moduleId,
              action.payload.xp
            )
            break
          case 'UPDATE_QUIZ_SCORE':
            await onUpdateQuizScore(
              action.payload.quizId,
              action.payload.score
            )
            break
        }
        
        this.removePendingAction(action.id)
        successCount++
      } catch (error) {
        console.error(`Failed to sync action ${action.id}:`, error)
        this.incrementRetryCount(action.id)
        failedCount++
      }
    }

    // Clean up actions that have failed too many times
    this.cleanupFailedActions()

    return { success: successCount, failed: failedCount }
  }

  // Check if we have offline data
  hasOfflineData(): boolean {
    return this.getProgress() !== null || this.getPendingActions().length > 0
  }

  // Get storage usage info
  getStorageInfo(): { used: number; available: number; percentage: number } {
    try {
      let used = 0
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length
        }
      }

      // Estimate available space (most browsers allow ~5-10MB)
      const estimated = 5 * 1024 * 1024 // 5MB
      const percentage = (used / estimated) * 100

      return {
        used,
        available: estimated - used,
        percentage: Math.min(percentage, 100)
      }
    } catch (error) {
      console.error('Failed to get storage info:', error)
      return { used: 0, available: 0, percentage: 0 }
    }
  }

  // Export data for backup
  exportData(): string {
    try {
      const progress = this.getProgress()
      const pendingActions = this.getPendingActions()
      
      return JSON.stringify({
        progress,
        pendingActions,
        exportDate: new Date().toISOString(),
        version: '1.0'
      }, null, 2)
    } catch (error) {
      console.error('Failed to export data:', error)
      return ''
    }
  }

  // Import data from backup
  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData)
      
      if (data.progress) {
        localStorage.setItem(this.PROGRESS_KEY, JSON.stringify(data.progress))
      }
      
      if (data.pendingActions) {
        localStorage.setItem(this.PENDING_ACTIONS_KEY, JSON.stringify(data.pendingActions))
      }
      
      return true
    } catch (error) {
      console.error('Failed to import data:', error)
      return false
    }
  }
}

export const offlineStorage = new OfflineStorageService()

// Hook for using offline storage in React components
export function useOfflineStorage() {
  const [isOnline, setIsOnline] = useState(true)
  const [pendingCount, setPendingCount] = useState(0)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    // Check initial status
    setIsOnline(navigator.onLine)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Update pending count
    const updatePendingCount = () => {
      setPendingCount(offlineStorage.getPendingActions().length)
    }
    
    updatePendingCount()
    const interval = setInterval(updatePendingCount, 5000) // Check every 5 seconds

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      clearInterval(interval)
    }
  }, [])

  return {
    isOnline,
    pendingCount,
    hasOfflineData: offlineStorage.hasOfflineData(),
    storageInfo: offlineStorage.getStorageInfo()
  }
}

import { useState, useEffect } from 'react'
