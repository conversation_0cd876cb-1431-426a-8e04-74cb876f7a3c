'use client'

import { motion } from 'framer-motion'
import { BookOpen, Target, Users, Zap, ArrowRight, CheckCircle } from 'lucide-react'
import { useGame } from '@/contexts/GameContext'
import { ModuleCard } from '@/components/LearningModules/ModuleCard'
import { AuthGuard } from '@/components/Auth/AuthGuard'

export default function ModulesPage() {
  const { gameState } = useGame()

  const modules = [
    {
      id: 'foundations',
      title: 'Social Media Foundations',
      description: 'Master the fundamentals of social media marketing, sales funnels, and essential tools. Learn about platform importance, creating effective sales funnels, benefits for local businesses, and discover the tools that will make your marketing efforts more efficient.',
      level: 'Beginner',
      chapters: 12,
      estimatedTime: '4-6 hours',
      color: 'from-blue-500 to-cyan-500',
      icon: BookOpen,
      topics: [
        'Importance of Social Media Marketing',
        'Creating the Sales Funnel',
        'Benefits for Local Businesses',
        'Essential Tools and Plugins',
        'Instagram Marketing Basics',
        'Twitter Marketing',
        'Facebook Marketing',
        'Reddit and Facebook Ads',
        'Snapchat Marketing',
        'Google Platform',
        'LinkedIn Strategies',
        'Pinterest Marketing'
      ]
    },
    {
      id: 'instagram',
      title: 'Instagram Mastery',
      description: 'Become an Instagram expert with advanced strategies, influencer marketing, and content creation. Learn how to create a branded account, develop effective marketing strategies, work with influencers, and master Instagram advertising.',
      level: 'Intermediate',
      chapters: 15,
      estimatedTime: '6-8 hours',
      color: 'from-pink-500 to-purple-500',
      icon: Target,
      topics: [
        'Using Instagram for Marketing',
        'Getting Started on Instagram',
        'Creating A Branded Account',
        'Effective Instagram Marketing Strategy',
        'The Importance of Influencers',
        'How to Search for An Influencer',
        'The 5 Unwritten Rules of Instagram',
        'Hacks for Taking Good Photos',
        'Instagram Automation',
        'Taking Advantage of Instagram Ads',
        'Retargeting Customers on Instagram',
        'Use Location (Geotag)',
        'Organize Contests and Giveaways',
        'The 14 Secrets to Engaging Your Audience',
        'Understanding Analytics'
      ]
    },
    {
      id: 'facebook',
      title: 'Facebook Advertising Pro',
      description: 'Master Facebook advertising, business pages, targeting, and campaign optimization. Learn everything about Facebook Pages, insights, branding, SEO strategies, and advanced advertising techniques to maximize your ROI.',
      level: 'Advanced',
      chapters: 15,
      estimatedTime: '6-8 hours',
      color: 'from-blue-600 to-indigo-600',
      icon: Users,
      topics: [
        'Everything about Facebook Pages',
        'Facebook Insights',
        'Branding Strategies',
        'Search Engine Optimization',
        'Facebook Advertising',
        'Marketing as a Two-way Street',
        'Pre-selling Your Audience',
        'Sales Funnel Optimization',
        'Improve, Test, Grow, and Monetize',
        'Analyzing and Retargeting',
        'Scheduling Content',
        'Maximizing Organic Reach',
        'Focus on Cost Per Action (CPA)',
        'Using the Pixel to Improve Ad Targeting',
        'Common Mistakes and How to Avoid Them'
      ]
    },
    {
      id: 'google',
      title: 'Google AdWords Expert',
      description: 'Dominate Google advertising with SEO, analytics, and advanced campaign strategies. Master keyword research, local SEO, PPC vs SEO, quality scores, and learn how to create compelling ads that convert.',
      level: 'Expert',
      chapters: 14,
      estimatedTime: '5-7 hours',
      color: 'from-green-500 to-emerald-500',
      icon: Zap,
      topics: [
        'Choosing Keywords',
        'Local SEO Begins at Home',
        'PPC vs. SEO',
        'Website Content that is Keyword Specific',
        'Google Quality Score',
        'Creating Compelling Ads',
        'Optimizing for Conversions',
        'Tips for Content Marketing Strategy',
        'Search Campaigns Ad Group Settings',
        'Facebook and Paid Advertising',
        'YouTube Marketing',
        'Converting Your Followers',
        'Making Your Landing Page Effective',
        'How to Optimize Your AdWords Campaign'
      ]
    }
  ]

  const learningPath = [
    { module: 'foundations', title: 'Start Here', description: 'Build your foundation' },
    { module: 'instagram', title: 'Visual Marketing', description: 'Master Instagram' },
    { module: 'facebook', title: 'Advanced Advertising', description: 'Facebook expertise' },
    { module: 'google', title: 'Search Mastery', description: 'Google domination' }
  ]

  return (
    <AuthGuard requireAuth={true}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-600 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Learning Modules
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Four comprehensive modules designed to transform you from beginner to expert in social media marketing.
            </p>
            <div className="flex items-center justify-center space-x-8 text-blue-100">
              <div className="text-center">
                <div className="text-3xl font-bold">{gameState.completedModules.length}</div>
                <div className="text-sm">Modules Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{gameState.completedChapters.length}</div>
                <div className="text-sm">Chapters Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{gameState.totalXP}</div>
                <div className="text-sm">Total XP</div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Learning Path */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Your Learning Path</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Follow this structured path to build your expertise step by step.
            </p>
          </motion.div>

          <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 mb-16">
            {learningPath.map((step, index) => {
              const isCompleted = gameState.completedModules.includes(step.module)
              const isCurrent = !isCompleted && (index === 0 || gameState.completedModules.includes(learningPath[index - 1].module))
              
              return (
                <motion.div
                  key={step.module}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="flex items-center"
                >
                  <div className={`relative p-4 rounded-xl border-2 transition-all duration-300 ${
                    isCompleted
                      ? 'bg-green-100 border-green-300'
                      : isCurrent
                      ? 'bg-primary-100 border-primary-300 ring-4 ring-primary-100'
                      : 'bg-gray-100 border-gray-300'
                  }`}>
                    <div className="text-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 mx-auto ${
                        isCompleted
                          ? 'bg-green-500 text-white'
                          : isCurrent
                          ? 'bg-primary-500 text-white'
                          : 'bg-gray-400 text-white'
                      }`}>
                        {isCompleted ? (
                          <CheckCircle className="w-5 h-5" />
                        ) : (
                          <span className="text-sm font-bold">{index + 1}</span>
                        )}
                      </div>
                      <div className="font-semibold text-gray-900">{step.title}</div>
                      <div className="text-sm text-gray-600">{step.description}</div>
                    </div>
                  </div>
                  
                  {index < learningPath.length - 1 && (
                    <ArrowRight className="w-6 h-6 text-gray-400 mx-4 hidden md:block" />
                  )}
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Modules Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-8">
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ModuleCard module={module} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold mb-6">Ready to Start Learning?</h2>
            <p className="text-xl mb-8 text-gray-300 max-w-2xl mx-auto">
              Begin with the Social Media Foundations module and work your way up to becoming a social media marketing expert.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary text-lg px-8 py-4 bg-primary-600 hover:bg-primary-700"
              onClick={() => {
                const firstIncompleteModule = modules.find(m => !gameState.completedModules.includes(m.id))
                if (firstIncompleteModule) {
                  window.location.href = `/modules/${firstIncompleteModule.id}`
                }
              }}
            >
              {gameState.completedChapters.length > 0 ? 'Continue Learning' : 'Start Your Journey'}
            </motion.button>
          </motion.div>
        </div>
      </section>
      </div>
    </AuthGuard>
  )
}
