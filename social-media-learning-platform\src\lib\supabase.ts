import { createClient } from '@/utils/supabase/client'

// Export the client for backward compatibility
export const supabase = createClient()

// Database Types
export interface UserProfile {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
  preferences: {
    theme?: 'light' | 'dark'
    notifications?: boolean
    emailUpdates?: boolean
    audioEnabled?: boolean
  }
  subscription_type: 'free' | 'premium' | 'enterprise'
  subscription_expires_at?: string
}

export interface LearningProgress {
  id: string
  user_id: string
  total_xp: number
  level: number
  completed_modules: string[]
  completed_chapters: string[]
  quiz_scores: Record<string, number>
  time_spent: number
  streak_days: number
  last_active_date: string
  created_at: string
  updated_at: string
}

export interface Achievement {
  id: string
  user_id: string
  achievement_id: string
  title: string
  description: string
  icon: string
  category: string
  unlocked_at: string
}

export interface UserSession {
  id: string
  user_id: string
  session_start: string
  session_end?: string
  pages_visited: string[]
  chapters_completed: string[]
  xp_earned: number
  device_type?: string
  browser?: string
  ip_address?: string
}

export interface ContentAnalytics {
  id: string
  user_id: string
  content_type: 'chapter' | 'quiz' | 'interactive'
  content_id: string
  action: 'view' | 'complete' | 'skip' | 'retry'
  time_spent?: number
  score?: number
  created_at: string
}

// Database operations
export class SupabaseService {
  // User Profile Operations
  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching user profile:', error)
      return null
    }
  }

  static async createUserProfile(profile: Partial<UserProfile>): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .insert([profile])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating user profile:', error)
      return null
    }
  }

  static async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating user profile:', error)
      return null
    }
  }

  // Learning Progress Operations
  static async getLearningProgress(userId: string): Promise<LearningProgress | null> {
    try {
      const { data, error } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching learning progress:', error)
      return null
    }
  }

  static async updateLearningProgress(
    userId: string, 
    updates: Partial<LearningProgress>
  ): Promise<LearningProgress | null> {
    try {
      const { data, error } = await supabase
        .from('learning_progress')
        .upsert({
          user_id: userId,
          ...updates,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating learning progress:', error)
      return null
    }
  }

  static async completeChapter(
    userId: string,
    moduleId: string,
    chapterId: string,
    xpGained: number,
    retries: number = 3
  ): Promise<boolean> {
    // Always use URL format: foundations-chapter-1, foundations-chapter-2, etc.
    const chapterKey = `${moduleId}-${chapterId}`

    console.log('🔄 SupabaseService.completeChapter called:', {
      userId,
      moduleId,
      chapterId,
      chapterKey,
      xpGained,
      retries,
      note: 'Using URL format for database consistency'
    })

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`📡 Attempt ${attempt}/${retries}: Calling update_user_progress RPC...`)

        const { data, error } = await supabase.rpc('update_user_progress', {
          p_user_id: userId,
          p_xp_gained: xpGained,
          p_chapter_id: chapterKey,
          p_module_id: null
        })

        console.log('📡 RPC Response:', { data, error })

        if (error) {
          console.error(`❌ Chapter completion attempt ${attempt} failed:`, {
            error,
            errorMessage: error.message,
            errorCode: error.code,
            errorDetails: error.details
          })

          if (attempt === retries) {
            throw error
          }
          // Wait before retry (exponential backoff)
          const waitTime = Math.pow(2, attempt) * 1000
          console.log(`⏳ Waiting ${waitTime}ms before retry...`)
          await new Promise(resolve => setTimeout(resolve, waitTime))
          continue
        }

        console.log('✅ Chapter completion successful via RPC')
        return true
      } catch (error) {
        console.error(`❌ Error completing chapter (attempt ${attempt}):`, {
          error,
          errorMessage: error instanceof Error ? error.message : error,
          stack: error instanceof Error ? error.stack : undefined
        })

        if (attempt === retries) {
          // Final attempt failed, try fallback approach
          console.log('🔄 All RPC attempts failed, trying fallback approach...')
          return await this.fallbackCompleteChapter(userId, moduleId, chapterId, xpGained)
        }
        // Wait before retry
        const waitTime = Math.pow(2, attempt) * 1000
        console.log(`⏳ Waiting ${waitTime}ms before retry...`)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
    return false
  }

  // Fallback method using direct table updates
  private static async fallbackCompleteChapter(
    userId: string,
    moduleId: string,
    chapterId: string,
    xpGained: number
  ): Promise<boolean> {
    const chapterKey = `${moduleId}-${chapterId}`

    console.log('🔄 Starting fallback chapter completion:', {
      userId,
      moduleId,
      chapterId,
      chapterKey,
      xpGained
    })

    try {
      // Get current progress
      console.log('📊 Fetching current progress from database...')
      const { data: currentProgress, error: fetchError } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', userId)
        .single()

      console.log('📊 Current progress fetch result:', { currentProgress, fetchError })

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('❌ Failed to fetch current progress:', fetchError)
        throw fetchError
      }

      const completedChapters = currentProgress?.completed_chapters || []

      console.log('📋 Current completed chapters:', completedChapters)

      // Check if already completed
      if (completedChapters.includes(chapterKey)) {
        console.log('⚠️ Chapter already completed in database:', chapterKey)
        return true
      }

      const newTotalXP = (currentProgress?.total_xp || 0) + xpGained
      const newLevel = Math.floor(newTotalXP / 1000) + 1
      const newCompletedChapters = [...completedChapters, chapterKey]

      console.log('💾 Preparing to upsert progress:', {
        newTotalXP,
        newLevel,
        newCompletedChapters
      })

      // Update or insert progress
      const { data: upsertData, error: upsertError } = await supabase
        .from('learning_progress')
        .upsert({
          user_id: userId,
          total_xp: newTotalXP,
          level: newLevel,
          completed_chapters: newCompletedChapters,
          completed_modules: currentProgress?.completed_modules || [],
          quiz_scores: currentProgress?.quiz_scores || {},
          time_spent: currentProgress?.time_spent || 0,
          streak_days: currentProgress?.streak_days || 0,
          last_active_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()

      console.log('💾 Upsert result:', { upsertData, upsertError })

      if (upsertError) {
        console.error('❌ Upsert failed:', upsertError)
        throw upsertError
      }

      console.log('✅ Fallback chapter completion successful')
      return true
    } catch (error) {
      console.error('❌ Fallback chapter completion failed:', {
        error,
        errorMessage: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined
      })
      return false
    }
  }

  static async completeModule(
    userId: string, 
    moduleId: string, 
    xpGained: number
  ): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('update_user_progress', {
        p_user_id: userId,
        p_xp_gained: xpGained,
        p_chapter_id: null,
        p_module_id: moduleId
      })

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error completing module:', error)
      return false
    }
  }

  // Achievement Operations
  static async getUserAchievements(userId: string): Promise<Achievement[]> {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .select('*')
        .eq('user_id', userId)
        .order('unlocked_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching achievements:', error)
      return []
    }
  }

  static async unlockAchievement(
    userId: string, 
    achievementData: Omit<Achievement, 'id' | 'user_id' | 'unlocked_at'>
  ): Promise<Achievement | null> {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .insert([{
          user_id: userId,
          ...achievementData,
          unlocked_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error unlocking achievement:', error)
      return null
    }
  }

  // Analytics Operations
  static async trackContentInteraction(
    userId: string,
    contentType: ContentAnalytics['content_type'],
    contentId: string,
    action: ContentAnalytics['action'],
    timeSpent?: number,
    score?: number
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('content_analytics')
        .insert([{
          user_id: userId,
          content_type: contentType,
          content_id: contentId,
          action,
          time_spent: timeSpent,
          score,
          created_at: new Date().toISOString()
        }])

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error tracking content interaction:', error)
      return false
    }
  }

  static async startUserSession(userId: string, deviceInfo?: Partial<UserSession>): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .insert([{
          user_id: userId,
          session_start: new Date().toISOString(),
          pages_visited: [],
          chapters_completed: [],
          xp_earned: 0,
          ...deviceInfo
        }])
        .select('id')
        .single()

      if (error) throw error
      return data.id
    } catch (error) {
      console.error('Error starting user session:', error)
      return null
    }
  }

  static async endUserSession(sessionId: string, updates: Partial<UserSession>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({
          ...updates,
          session_end: new Date().toISOString()
        })
        .eq('id', sessionId)

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error ending user session:', error)
      return false
    }
  }

  // Real-time subscriptions
  static subscribeToProgressUpdates(
    userId: string, 
    callback: (payload: any) => void
  ) {
    return supabase
      .channel(`progress_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'learning_progress',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe()
  }

  static subscribeToAchievements(
    userId: string, 
    callback: (payload: any) => void
  ) {
    return supabase
      .channel(`achievements_${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'achievements',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe()
  }
}

// Authentication helpers
export const auth = {
  signUp: async (email: string, password: string, metadata?: any) => {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
  },

  signIn: async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({
      email,
      password
    })
  },

  signOut: async () => {
    return await supabase.auth.signOut()
  },

  resetPassword: async (email: string) => {
    return await supabase.auth.resetPasswordForEmail(email)
  },

  getUser: async () => {
    return await supabase.auth.getUser()
  },

  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback)
  }
}

export default supabase
