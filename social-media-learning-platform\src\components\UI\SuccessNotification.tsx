'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, X, Cloud, CloudOff, Wifi, WifiOff } from 'lucide-react'

interface SuccessNotificationProps {
  isVisible: boolean
  title: string
  message: string
  type?: 'success' | 'warning' | 'info'
  duration?: number
  onClose?: () => void
  showSyncStatus?: boolean
  isOnline?: boolean
}

export function SuccessNotification({
  isVisible,
  title,
  message,
  type = 'success',
  duration = 5000,
  onClose,
  showSyncStatus = false,
  isOnline = true
}: SuccessNotificationProps) {
  const [isShowing, setIsShowing] = useState(isVisible)

  useEffect(() => {
    setIsShowing(isVisible)
    
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        setIsShowing(false)
        setTimeout(() => onClose?.(), 300) // Wait for animation to complete
      }, duration)
      
      return () => clearTimeout(timer)
    }
  }, [isVisible, duration, onClose])

  const handleClose = () => {
    setIsShowing(false)
    setTimeout(() => onClose?.(), 300)
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-6 h-6 text-green-600" />
      case 'warning':
        return <Cloud className="w-6 h-6 text-yellow-600" />
      case 'info':
        return <Cloud className="w-6 h-6 text-blue-600" />
      default:
        return <CheckCircle className="w-6 h-6 text-green-600" />
    }
  }

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800'
        }
      case 'warning':
        return {
          bg: 'bg-yellow-50',
          border: 'border-yellow-200',
          text: 'text-yellow-800'
        }
      case 'info':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          text: 'text-blue-800'
        }
      default:
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800'
        }
    }
  }

  const colors = getColors()

  return (
    <AnimatePresence>
      {isShowing && (
        <motion.div
          initial={{ opacity: 0, y: -100, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -100, scale: 0.95 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 300, damping: 30 }}
          className="fixed top-4 right-4 z-50 max-w-sm w-full"
        >
          <div className={`${colors.bg} ${colors.border} border rounded-xl p-4 shadow-lg backdrop-blur-sm`}>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getIcon()}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className={`font-semibold ${colors.text}`}>
                    {title}
                  </h4>
                  <button
                    onClick={handleClose}
                    className={`${colors.text} hover:opacity-70 transition-opacity`}
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
                
                <p className={`text-sm ${colors.text} opacity-90 mt-1`}>
                  {message}
                </p>
                
                {showSyncStatus && (
                  <div className="flex items-center space-x-2 mt-2">
                    {isOnline ? (
                      <>
                        <Cloud className="w-4 h-4 text-green-600" />
                        <span className="text-xs text-green-700 font-medium">
                          Synced to cloud
                        </span>
                      </>
                    ) : (
                      <>
                        <CloudOff className="w-4 h-4 text-gray-500" />
                        <span className="text-xs text-gray-600 font-medium">
                          Saved locally (will sync when online)
                        </span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
            
            {/* Progress bar for auto-dismiss */}
            {duration > 0 && (
              <motion.div
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: duration / 1000, ease: "linear" }}
                className="h-1 bg-current opacity-20 rounded-full mt-3"
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Hook for managing success notifications
export function useSuccessNotification() {
  const [notification, setNotification] = useState<{
    isVisible: boolean
    title: string
    message: string
    type: 'success' | 'warning' | 'info'
    showSyncStatus: boolean
    isOnline: boolean
  }>({
    isVisible: false,
    title: '',
    message: '',
    type: 'success',
    showSyncStatus: false,
    isOnline: true
  })

  const showNotification = (
    title: string,
    message: string,
    type: 'success' | 'warning' | 'info' = 'success',
    showSyncStatus: boolean = false
  ) => {
    setNotification({
      isVisible: true,
      title,
      message,
      type,
      showSyncStatus,
      isOnline: navigator.onLine
    })
  }

  const hideNotification = () => {
    setNotification(prev => ({ ...prev, isVisible: false }))
  }

  const showProgressSaved = (isOnline: boolean = navigator.onLine) => {
    showNotification(
      'Progress Saved!',
      isOnline 
        ? 'Your chapter completion has been saved to the cloud.'
        : 'Your progress has been saved locally and will sync when you\'re back online.',
      isOnline ? 'success' : 'warning',
      true
    )
  }

  const showChapterCompleted = (chapterTitle: string, xp: number, isOnline: boolean = navigator.onLine) => {
    showNotification(
      'Chapter Completed! 🎉',
      `You've completed "${chapterTitle}" and earned ${xp} XP!`,
      'success',
      true
    )
  }

  const showSyncSuccess = (itemCount: number) => {
    showNotification(
      'Sync Complete!',
      `Successfully synced ${itemCount} item${itemCount !== 1 ? 's' : ''} to the cloud.`,
      'success',
      true
    )
  }

  const showSyncError = () => {
    showNotification(
      'Sync Failed',
      'Unable to sync your progress. Your data is saved locally and we\'ll try again later.',
      'warning',
      true
    )
  }

  return {
    notification,
    showNotification,
    hideNotification,
    showProgressSaved,
    showChapterCompleted,
    showSyncSuccess,
    showSyncError,
    NotificationComponent: () => (
      <SuccessNotification
        isVisible={notification.isVisible}
        title={notification.title}
        message={notification.message}
        type={notification.type}
        showSyncStatus={notification.showSyncStatus}
        isOnline={notification.isOnline}
        onClose={hideNotification}
      />
    )
  }
}
