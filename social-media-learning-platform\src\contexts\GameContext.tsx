'use client'

import React, { createContext, useContext, useReducer, useEffect, useState } from 'react'
import { SupabaseService } from '@/lib/supabase'
import { useAuth } from './AuthContext'
import { offlineStorage } from '@/lib/offlineStorage'

export interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  unlockedAt?: Date
  category: 'learning' | 'engagement' | 'completion' | 'special'
}

export interface GameState {
  totalXP: number
  level: number
  currentModule: string | null
  completedModules: string[]
  completedChapters: string[]
  achievements: Achievement[]
  streakDays: number
  lastActiveDate: string | null
  moduleProgress: Record<string, number>
  quizScores: Record<string, number>
}

type GameAction =
  | { type: 'COMPLETE_CHAPTER'; payload: { moduleId: string; chapterId: string; xp: number } }
  | { type: 'COMPLETE_MODULE'; payload: { moduleId: string; xp: number } }
  | { type: 'UNLOCK_ACHIEVEMENT'; payload: Achievement }
  | { type: 'UPDATE_QUIZ_SCORE'; payload: { quizId: string; score: number } }
  | { type: 'SET_CURRENT_MODULE'; payload: string }
  | { type: 'UPDATE_STREAK'; payload: number }
  | { type: 'LOAD_GAME_STATE'; payload: GameState }

const initialState: GameState = {
  totalXP: 0,
  level: 1,
  currentModule: null,
  completedModules: [],
  completedChapters: [],
  achievements: [],
  streakDays: 0,
  lastActiveDate: null,
  moduleProgress: {},
  quizScores: {}
}

const achievements: Achievement[] = [
  {
    id: 'first-steps',
    title: 'First Steps',
    description: 'Complete your first chapter',
    icon: '🎯',
    category: 'learning'
  },
  {
    id: 'social-novice',
    title: 'Social Media Novice',
    description: 'Complete the Social Media Foundations module',
    icon: '📚',
    category: 'completion'
  },
  {
    id: 'instagram-influencer',
    title: 'Instagram Influencer',
    description: 'Complete the Instagram Mastery module',
    icon: '📸',
    category: 'completion'
  },
  {
    id: 'facebook-pro',
    title: 'Facebook Pro',
    description: 'Complete the Facebook Advertising module',
    icon: '👥',
    category: 'completion'
  },
  {
    id: 'google-guru',
    title: 'Google Guru',
    description: 'Complete the Google AdWords module',
    icon: '🔍',
    category: 'completion'
  },
  {
    id: 'quiz-master',
    title: 'Quiz Master',
    description: 'Score 90% or higher on 5 quizzes',
    icon: '🧠',
    category: 'engagement'
  },
  {
    id: 'streak-warrior',
    title: 'Streak Warrior',
    description: 'Maintain a 7-day learning streak',
    icon: '🔥',
    category: 'engagement'
  },
  {
    id: 'social-media-master',
    title: 'Social Media Master',
    description: 'Complete all modules with excellence',
    icon: '👑',
    category: 'special'
  }
]

function calculateLevel(xp: number): number {
  return Math.floor(xp / 1000) + 1
}

function gameReducer(state: GameState, action: GameAction): GameState {
  switch (action.type) {
    case 'COMPLETE_CHAPTER': {
      const { moduleId, chapterId, xp } = action.payload
      const newTotalXP = state.totalXP + xp
      const newLevel = calculateLevel(newTotalXP)
      const chapterKey = `${moduleId}-${chapterId}`
      
      if (state.completedChapters.includes(chapterKey)) {
        return state // Already completed
      }

      const newCompletedChapters = [...state.completedChapters, chapterKey]
      const moduleChapters = newCompletedChapters.filter(ch => ch.startsWith(moduleId))
      const moduleProgress = { ...state.moduleProgress, [moduleId]: moduleChapters.length }

      return {
        ...state,
        totalXP: newTotalXP,
        level: newLevel,
        completedChapters: newCompletedChapters,
        moduleProgress,
        lastActiveDate: new Date().toISOString()
      }
    }

    case 'COMPLETE_MODULE': {
      const { moduleId, xp } = action.payload
      const newTotalXP = state.totalXP + xp
      const newLevel = calculateLevel(newTotalXP)
      
      if (state.completedModules.includes(moduleId)) {
        return state // Already completed
      }

      return {
        ...state,
        totalXP: newTotalXP,
        level: newLevel,
        completedModules: [...state.completedModules, moduleId],
        lastActiveDate: new Date().toISOString()
      }
    }

    case 'UNLOCK_ACHIEVEMENT': {
      const achievement = action.payload
      if (state.achievements.some(a => a.id === achievement.id)) {
        return state // Already unlocked
      }

      return {
        ...state,
        achievements: [...state.achievements, { ...achievement, unlockedAt: new Date() }]
      }
    }

    case 'UPDATE_QUIZ_SCORE': {
      const { quizId, score } = action.payload
      return {
        ...state,
        quizScores: { ...state.quizScores, [quizId]: score }
      }
    }

    case 'SET_CURRENT_MODULE': {
      return {
        ...state,
        currentModule: action.payload
      }
    }

    case 'UPDATE_STREAK': {
      return {
        ...state,
        streakDays: action.payload
      }
    }

    case 'LOAD_GAME_STATE': {
      return action.payload
    }

    default:
      return state
  }
}

const GameContext = createContext<{
  gameState: GameState
  dispatch: React.Dispatch<GameAction>
  completeChapter: (moduleId: string, chapterId: string, xp?: number) => Promise<void>
  completeModule: (moduleId: string, xp?: number) => void
  updateQuizScore: (quizId: string, score: number) => void
  setCurrentModule: (moduleId: string) => void
  checkAchievements: () => void
  celebrationTriggers: {
    achievement: boolean
    levelUp: boolean
    moduleComplete: boolean
  }
  resetCelebrationTrigger: (type: 'achievement' | 'levelUp' | 'moduleComplete') => void
  isLoading: boolean
} | null>(null)

export function GameProvider({ children }: { children: React.ReactNode }) {
  const [gameState, dispatch] = useReducer(gameReducer, initialState)
  const [celebrationTriggers, setCelebrationTriggers] = useState({
    achievement: false,
    levelUp: false,
    moduleComplete: false
  })
  const { authState } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  // Load game state from Supabase when user is authenticated
  useEffect(() => {
    const loadGameState = async () => {
      if (authState.isAuthenticated && authState.user) {
        setIsLoading(true)
        try {
          const progress = await SupabaseService.getLearningProgress(authState.user.id)
          const achievements = await SupabaseService.getUserAchievements(authState.user.id)

          if (progress) {
            const gameStateFromDB: GameState = {
              totalXP: progress.total_xp,
              level: progress.level,
              currentModule: null,
              completedModules: progress.completed_modules,
              completedChapters: progress.completed_chapters,
              achievements: achievements.map(a => ({
                id: a.achievement_id,
                title: a.title,
                description: a.description,
                icon: a.icon,
                category: a.category as any,
                unlockedAt: new Date(a.unlocked_at)
              })),
              streakDays: progress.streak_days,
              lastActiveDate: progress.last_active_date,
              moduleProgress: progress.completed_chapters.reduce((acc, chapter) => {
                const [moduleId] = chapter.split('-')
                acc[moduleId] = (acc[moduleId] || 0) + 1
                return acc
              }, {} as Record<string, number>),
              quizScores: progress.quiz_scores
            }

            console.log('✅ Loaded game state from Supabase (DATABASE IS SOURCE OF TRUTH):', {
              totalXP: gameStateFromDB.totalXP,
              level: gameStateFromDB.level,
              completedChapters: gameStateFromDB.completedChapters,
              completedModules: gameStateFromDB.completedModules
            })

            // Clear localStorage to prevent conflicts - database is source of truth
            if (typeof window !== 'undefined') {
              localStorage.removeItem('socialMediaGameState')
              console.log('🧹 Cleared localStorage - database is now source of truth')
            }

            dispatch({ type: 'LOAD_GAME_STATE', payload: gameStateFromDB })
          } else {
            console.log('📝 No progress found in Supabase, creating new record')
            // Create initial progress record
            await SupabaseService.updateLearningProgress(authState.user.id, {
              total_xp: 0,
              level: 1,
              completed_modules: [],
              completed_chapters: [],
              quiz_scores: {},
              streak_days: 0,
              last_active_date: new Date().toISOString()
            })
          }
        } catch (error) {
          console.error('❌ Failed to load game state from Supabase:', error)
          // Fallback to localStorage only if database fails
          if (typeof window !== 'undefined') {
            const savedState = localStorage.getItem('socialMediaGameState')
            if (savedState) {
              try {
                const parsedState = JSON.parse(savedState)
                console.log('⚠️ Using localStorage fallback due to database error')
                dispatch({ type: 'LOAD_GAME_STATE', payload: parsedState })
              } catch (parseError) {
                console.error('Failed to parse localStorage state:', parseError)
              }
            }
          }
        } finally {
          setIsLoading(false)
        }
      } else if (!authState.isAuthenticated) {
        // Load from localStorage for non-authenticated users
        if (typeof window !== 'undefined') {
          const savedState = localStorage.getItem('socialMediaGameState')
          if (savedState) {
            try {
              const parsedState = JSON.parse(savedState)
              dispatch({ type: 'LOAD_GAME_STATE', payload: parsedState })
            } catch (error) {
              console.error('Failed to load game state:', error)
            }
          }
        }
      }
    }

    loadGameState()
  }, [authState.isAuthenticated, authState.user])

  // Save game state - prioritize database for authenticated users
  useEffect(() => {
    const saveGameState = async () => {
      // Save to Supabase if user is authenticated (PRIMARY)
      if (authState.isAuthenticated && authState.user && !isLoading) {
        try {
          console.log('💾 Saving to Supabase (primary storage for authenticated users):', {
            totalXP: gameState.totalXP,
            level: gameState.level,
            completedChapters: gameState.completedChapters,
            completedModules: gameState.completedModules
          })

          const result = await SupabaseService.updateLearningProgress(authState.user.id, {
            total_xp: gameState.totalXP,
            level: gameState.level,
            completed_modules: gameState.completedModules,
            completed_chapters: gameState.completedChapters,
            quiz_scores: gameState.quizScores,
            streak_days: gameState.streakDays,
            last_active_date: gameState.lastActiveDate || new Date().toISOString()
          })

          if (result) {
            console.log('✅ Successfully saved to Supabase database')
            // Clear localStorage since database is source of truth
            if (typeof window !== 'undefined') {
              localStorage.removeItem('socialMediaGameState')
            }
          } else {
            console.warn('⚠️ Failed to save to database - keeping localStorage as backup')
            // Only save to localStorage if database fails
            if (typeof window !== 'undefined') {
              localStorage.setItem('socialMediaGameState', JSON.stringify(gameState))
            }
          }
        } catch (error) {
          console.error('❌ Failed to save game state to Supabase:', error)
          // Fallback to localStorage only on error
          if (typeof window !== 'undefined') {
            localStorage.setItem('socialMediaGameState', JSON.stringify(gameState))
            console.log('💾 Saved to localStorage as fallback')
          }
        }
      } else if (!authState.isAuthenticated) {
        // Save to localStorage for non-authenticated users only
        if (typeof window !== 'undefined') {
          localStorage.setItem('socialMediaGameState', JSON.stringify(gameState))
          console.log('💾 Saved to localStorage (non-authenticated user)')
        }
      }
    }

    // Debounce saves to avoid too frequent updates
    const timeoutId = setTimeout(saveGameState, 1000)
    return () => clearTimeout(timeoutId)
  }, [gameState, authState.isAuthenticated, authState.user, isLoading])

  // Sync pending actions when coming back online
  useEffect(() => {
    const handleOnline = async () => {
      if (authState.isAuthenticated && authState.user) {
        try {
          const result = await offlineStorage.syncPendingActions(
            async (moduleId: string, chapterId: string, xp: number) => {
              // Use the Supabase service directly to avoid infinite loops
              await SupabaseService.completeChapter(authState.user!.id, moduleId, chapterId, xp)
            },
            async (moduleId: string, xp: number) => {
              // Implement module completion sync if needed
              console.log('Syncing module completion:', moduleId, xp)
            },
            async (quizId: string, score: number) => {
              // Implement quiz score sync if needed
              console.log('Syncing quiz score:', quizId, score)
            }
          )

          if (result.success > 0) {
            console.log(`Successfully synced ${result.success} pending actions`)
          }

          if (result.failed > 0) {
            console.warn(`Failed to sync ${result.failed} pending actions`)
          }
        } catch (error) {
          console.error('Failed to sync pending actions:', error)
        }
      }
    }

    window.addEventListener('online', handleOnline)
    return () => window.removeEventListener('online', handleOnline)
  }, [authState.isAuthenticated, authState.user])

  const completeChapter = async (moduleId: string, chapterId: string, xp: number = 100): Promise<void> => {
    const oldLevel = gameState.level
    const chapterKey = `${moduleId}-${chapterId}`

    console.log('🎮 GameContext.completeChapter called:', {
      moduleId,
      chapterId,
      chapterKey,
      xp,
      currentCompletedChapters: gameState.completedChapters,
      isAuthenticated: authState.isAuthenticated,
      userId: authState.user?.id,
      isOnline: navigator.onLine,
      note: 'Using URL format for consistent database operations'
    })

    // Check if already completed
    if (gameState.completedChapters.includes(chapterKey)) {
      console.log('⚠️ Chapter already completed, skipping:', chapterKey)
      return Promise.resolve()
    }

    try {
      // Update local state first
      console.log('📝 Dispatching COMPLETE_CHAPTER action...')
      dispatch({ type: 'COMPLETE_CHAPTER', payload: { moduleId, chapterId, xp } })

      // Save to offline storage immediately
      const newCompletedChapters = [...gameState.completedChapters, chapterKey]
      const newTotalXP = gameState.totalXP + xp
      const newLevel = calculateLevel(newTotalXP)

      console.log('💾 Saving to offline storage:', {
        newCompletedChapters,
        newTotalXP,
        newLevel
      })

      offlineStorage.saveProgress({
        userId: authState.user?.id || 'anonymous',
        completedChapters: newCompletedChapters,
        totalXP: newTotalXP,
        level: newLevel,
        lastActiveDate: new Date().toISOString()
      })

      // Try to save to Supabase if user is authenticated and online
      if (authState.isAuthenticated && authState.user && navigator.onLine) {
        try {
          console.log('🔄 Attempting to save to Supabase database...')
          const success = await SupabaseService.completeChapter(
            authState.user.id,
            moduleId,
            chapterId,
            xp
          )

          if (!success) {
            throw new Error('Failed to save chapter completion to database')
          }

          console.log('✅ Successfully saved to Supabase database')
        } catch (dbError) {
          console.warn('⚠️ Failed to save to database, storing for later sync:', dbError)
          // Add to pending actions for later sync
          offlineStorage.addPendingAction({
            type: 'COMPLETE_CHAPTER',
            payload: { moduleId, chapterId, xp }
          })
        }
      } else if (authState.isAuthenticated && authState.user) {
        // User is authenticated but offline, store for later sync
        console.log('📱 User offline, storing for later sync')
        offlineStorage.addPendingAction({
          type: 'COMPLETE_CHAPTER',
          payload: { moduleId, chapterId, xp }
        })
      } else {
        console.log('👤 User not authenticated, progress saved locally only')
      }

      // Check for level up
      if (newLevel > oldLevel) {
        console.log('🎉 Level up detected!', { oldLevel, newLevel })
        setCelebrationTriggers(prev => ({ ...prev, levelUp: true }))
      }

      // Check for achievements
      console.log('🏆 Checking for achievements...')
      checkAchievements()

      console.log('✅ Chapter completion process finished successfully')

    } catch (error) {
      console.error('❌ Error in completeChapter:', error)
      throw error
    }
  }

  const completeModule = (moduleId: string, xp: number = 500) => {
    const oldLevel = gameState.level
    dispatch({ type: 'COMPLETE_MODULE', payload: { moduleId, xp } })
    setCelebrationTriggers(prev => ({ ...prev, moduleComplete: true }))

    // Check for level up after state update
    setTimeout(() => {
      const newLevel = calculateLevel(gameState.totalXP + xp)
      if (newLevel > oldLevel) {
        setCelebrationTriggers(prev => ({ ...prev, levelUp: true }))
      }
    }, 100)
  }

  const updateQuizScore = (quizId: string, score: number) => {
    dispatch({ type: 'UPDATE_QUIZ_SCORE', payload: { quizId, score } })
  }

  const setCurrentModule = (moduleId: string) => {
    dispatch({ type: 'SET_CURRENT_MODULE', payload: moduleId })
  }

  const checkAchievements = () => {
    achievements.forEach(achievement => {
      if (gameState.achievements.some(a => a.id === achievement.id)) {
        return // Already unlocked
      }

      let shouldUnlock = false

      switch (achievement.id) {
        case 'first-steps':
          shouldUnlock = gameState.completedChapters.length >= 1
          break
        case 'social-novice':
          shouldUnlock = gameState.completedModules.includes('foundations')
          break
        case 'instagram-influencer':
          shouldUnlock = gameState.completedModules.includes('instagram')
          break
        case 'facebook-pro':
          shouldUnlock = gameState.completedModules.includes('facebook')
          break
        case 'google-guru':
          shouldUnlock = gameState.completedModules.includes('google')
          break
        case 'quiz-master':
          const highScores = Object.values(gameState.quizScores).filter(score => score >= 90)
          shouldUnlock = highScores.length >= 5
          break
        case 'streak-warrior':
          shouldUnlock = gameState.streakDays >= 7
          break
        case 'social-media-master':
          shouldUnlock = gameState.completedModules.length === 4
          break
      }

      if (shouldUnlock) {
        dispatch({ type: 'UNLOCK_ACHIEVEMENT', payload: achievement })
        setCelebrationTriggers(prev => ({ ...prev, achievement: true }))
      }
    })
  }

  // Check achievements whenever game state changes
  useEffect(() => {
    checkAchievements()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState.completedChapters, gameState.completedModules, gameState.quizScores, gameState.streakDays])

  const resetCelebrationTrigger = (type: 'achievement' | 'levelUp' | 'moduleComplete') => {
    setCelebrationTriggers(prev => ({ ...prev, [type]: false }))
  }

  return (
    <GameContext.Provider value={{
      gameState,
      dispatch,
      completeChapter,
      completeModule,
      updateQuizScore,
      setCurrentModule,
      checkAchievements,
      celebrationTriggers,
      resetCelebrationTrigger,
      isLoading
    }}>
      {children}
    </GameContext.Provider>
  )
}

export function useGame() {
  const context = useContext(GameContext)
  if (!context) {
    throw new Error('useGame must be used within a GameProvider')
  }
  return context
}
