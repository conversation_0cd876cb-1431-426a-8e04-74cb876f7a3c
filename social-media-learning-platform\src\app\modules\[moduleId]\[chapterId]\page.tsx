'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { 
  ArrowLeft, 
  ArrowRight, 
  BookOpen, 
  CheckCircle, 
  Trophy, 
  Star,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react'
import Link from 'next/link'
import { useGame } from '@/contexts/GameContext'
import { useAudio } from '@/hooks/useAudio'
import { chapterContent } from '@/data/chapterContent'
import type { ChapterSection } from '@/data/chapterContent'
import { useSuccessNotification } from '@/components/UI/SuccessNotification'

// Chapter URL to content key mapping
const chapterMapping: Record<string, string> = {
  // Foundations module
  'foundations-chapter-1': 'foundations-importance',
  'foundations-chapter-2': 'foundations-sales-funnel',
  'foundations-chapter-3': 'foundations-local-benefits',
  'foundations-chapter-4': 'foundations-tools-plugins',
  'foundations-chapter-5': 'foundations-instagram-basics',
  'foundations-chapter-6': 'foundations-twitter-marketing',

  // Instagram module
  'instagram-chapter-1': 'instagram-marketing',
  'instagram-chapter-2': 'instagram-content-strategy',
  'instagram-chapter-3': 'instagram-advertising',
  'instagram-chapter-4': 'instagram-analytics',

  // Facebook module
  'facebook-chapter-1': 'facebook-marketing',
  'facebook-chapter-2': 'facebook-advertising',
  'facebook-chapter-3': 'facebook-analytics',
  'facebook-chapter-4': 'facebook-messenger',

  // Google module
  'google-chapter-1': 'google-adwords',
  'google-chapter-2': 'google-analytics',
  'google-chapter-3': 'google-search-console',
  'google-chapter-4': 'google-my-business'
}

// Fallback content for chapters not yet implemented
const fallbackContent = {
  'foundations-importance': {
    title: 'Importance of Social Media Marketing',
    sections: [
      {
        type: 'text',
        content: `Social media has radically changed how businesses interact with the people who are their customers and potential customers. For this reason, having a sophisticated social media marketing program is essential for your company's success.

There are a ton of benefits to making sure that your social media strategy is integrated with your traditional marketing initiatives. To that end, it is imperative to hire someone who has the expertise in this area to run it right.`
      },
      {
        type: 'interactive',
        title: 'Key Benefits of Social Media Marketing',
        content: 'Drag and drop the benefits to match them with their descriptions:',
        activity: {
          type: 'matching',
          items: [
            { id: 1, text: 'Increased Brand Awareness', match: 'Reach millions of potential customers globally' },
            { id: 2, text: 'Cost-Effective Marketing', match: 'Lower costs compared to traditional advertising' },
            { id: 3, text: 'Better Customer Engagement', match: 'Direct interaction with your audience' },
            { id: 4, text: 'Improved Customer Insights', match: 'Analytics provide valuable customer data' }
          ]
        }
      },
      {
        type: 'text',
        content: `About 93% of business people using social media to market their brands claim that it helps in increasing their business exposure. 71% of them argue that it helps in developing loyal fans.

Setting goals is vital to the success of your company and having tools to measure those goals is also essential. When it comes to goals, these goals should be all about making a profit while at the same time being attainable for employees.`
      },
      {
        type: 'quiz',
        title: 'Quick Knowledge Check',
        questions: [
          {
            question: 'What percentage of business people claim social media helps increase business exposure?',
            options: ['71%', '85%', '93%', '78%'],
            correct: 2
          },
          {
            question: 'What should business goals focus on according to the text?',
            options: ['Employee satisfaction', 'Making profit while being attainable', 'Brand recognition', 'Social media followers'],
            correct: 1
          }
        ]
      }
    ]
  }
}

export default function ChapterPage() {
  const params = useParams()
  const router = useRouter()
  const { gameState, completeChapter } = useGame()
  const { playFeedback } = useAudio()
  const { showChapterCompleted, showProgressSaved, NotificationComponent } = useSuccessNotification()
  
  const moduleId = params.moduleId as string
  const chapterId = params.chapterId as string
  const chapterKey = `${moduleId}-${chapterId}`

  // Get the actual content key using the mapping
  const contentKey = chapterMapping[chapterKey] || chapterKey

  const [currentSection, setCurrentSection] = useState(0)
  const [quizAnswers, setQuizAnswers] = useState<Record<number, number>>({})
  const [matchingAnswers, setMatchingAnswers] = useState<Record<number, number>>({})
  const [isCompleted, setIsCompleted] = useState(false)
  const [isCompleting, setIsCompleting] = useState(false)
  const [completionError, setCompletionError] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  // Get chapter content using the mapped content key
  const content = chapterContent[contentKey as keyof typeof chapterContent] || fallbackContent[contentKey as keyof typeof fallbackContent] || {
    title: 'Chapter Content',
    sections: [
      {
        type: 'text',
        content: `Welcome to this comprehensive chapter from the ${moduleId} module. This chapter is designed to provide you with practical, actionable knowledge that you can immediately apply to your social media marketing efforts.

**What You'll Learn:**
• Core concepts and strategies specific to this topic
• Real-world examples and case studies
• Interactive exercises to reinforce learning
• Best practices from industry experts
• Actionable takeaways you can implement today

**Learning Approach:**
This chapter uses a dynamic, content-driven structure that adapts to the complexity of each topic. Rather than a fixed format, each section builds upon the previous one, ensuring comprehensive coverage of the subject matter.

Let's begin your learning journey!`
      },
      {
        type: 'interactive',
        title: 'Learning Objectives',
        content: 'Check off each objective as you progress through the chapter:',
        activity: {
          type: 'checklist',
          items: [
            { id: 1, text: 'Understand the core concepts', completed: false },
            { id: 2, text: 'Apply knowledge through exercises', completed: false },
            { id: 3, text: 'Complete the chapter assessment', completed: false },
            { id: 4, text: 'Identify actionable next steps', completed: false }
          ]
        }
      },
      {
        type: 'quiz',
        title: 'Knowledge Check',
        questions: [
          {
            question: 'What makes this learning platform unique?',
            options: ['Fixed content structure', 'Dynamic, adaptive content', 'Static presentations', 'Basic information delivery'],
            correct: 1,
            explanation: 'This platform uses dynamic, adaptive content that adjusts to the complexity and requirements of each topic, ensuring comprehensive learning.'
          },
          {
            question: 'How should you approach each chapter?',
            options: ['Skip to the quiz', 'Read passively', 'Engage with all interactive elements', 'Focus only on text'],
            correct: 2,
            explanation: 'Active engagement with all interactive elements, exercises, and assessments maximizes learning outcomes and retention.'
          }
        ]
      }
    ]
  }
  
  const totalSections = content.sections.length
  const progress = ((currentSection + 1) / totalSections) * 100

  // Sync completion state with game state
  useEffect(() => {
    setIsCompleted(gameState.completedChapters.includes(chapterKey))
  }, [gameState.completedChapters, chapterKey])

  const handleNext = async () => {
    if (currentSection < totalSections - 1) {
      setCurrentSection(currentSection + 1)
    } else if (!isCompleted && !isCompleting) {
      // Complete the chapter only when user reaches the end
      setIsCompleting(true)
      setCompletionError(null)

      try {
        console.log('🎯 Starting chapter completion:', {
          moduleId,
          chapterId,
          chapterKey,
          contentKey,
          xp: 100
        })

        await completeChapter(moduleId, chapterId, 100)

        console.log('✅ Chapter completion successful:', {
          moduleId,
          chapterId,
          chapterKey,
          completedChapters: gameState.completedChapters
        })

        // Show success notification
        showChapterCompleted(content.title, 100, navigator.onLine)

        playFeedback('chapterComplete')
        // State will be updated via useEffect when gameState changes
      } catch (error) {
        console.error('❌ Failed to complete chapter:', {
          moduleId,
          chapterId,
          chapterKey,
          error: error instanceof Error ? error.message : error,
          stack: error instanceof Error ? error.stack : undefined
        })
        setCompletionError('Failed to save progress. Please try again.')
        playFeedback('error')
      } finally {
        setIsCompleting(false)
      }
    }
  }

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1)
    }
  }

  const handleQuizAnswer = (questionIndex: number, answerIndex: number) => {
    setQuizAnswers(prev => ({ ...prev, [questionIndex]: answerIndex }))

    // Get the question to check if answer is correct
    const section = content.sections[currentSection]
    if (section.type === 'quiz' && section.questions) {
      const question = section.questions[questionIndex]
      if (answerIndex === question.correct) {
        playFeedback('success')
      } else {
        playFeedback('error')
      }
    }
  }

  const handleMatchingAnswer = (itemId: number, matchId: number) => {
    setMatchingAnswers(prev => ({ ...prev, [itemId]: matchId }))
  }

  // Manual save function for current progress
  const handleManualSave = async () => {
    if (isSaving || isCompleting) return

    setIsSaving(true)
    setCompletionError(null)

    try {
      console.log('💾 Manual save triggered:', {
        moduleId,
        chapterId,
        chapterKey,
        currentSection,
        totalSections,
        progressPercentage: Math.round(progress)
      })

      // Calculate partial XP based on progress
      const partialXP = Math.round((progress / 100) * 100)

      await completeChapter(moduleId, chapterId, partialXP)

      showProgressSaved(navigator.onLine)
      playFeedback('success')

      console.log('✅ Manual save successful')
    } catch (error) {
      console.error('❌ Manual save failed:', error)
      setCompletionError('Failed to save progress. Please try again.')
      playFeedback('error')
    } finally {
      setIsSaving(false)
    }
  }

  const renderSection = (section: any, index: number) => {
    switch (section.type) {
      case 'text':
        return (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="prose prose-lg max-w-none"
          >
            <div className="text-gray-700 leading-relaxed whitespace-pre-line">
              {section.content}
            </div>
          </motion.div>
        )

      case 'interactive':
        if (section.activity?.type === 'matching') {
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-blue-50 rounded-xl p-6"
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">{section.title}</h3>
              <p className="text-gray-600 mb-6">{section.content}</p>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Benefits</h4>
                  <div className="space-y-2">
                    {section.activity.items.map((item: any) => (
                      <motion.div
                        key={item.id}
                        whileHover={{ scale: 1.02 }}
                        className="p-3 bg-white rounded-lg border-2 border-gray-200 cursor-pointer hover:border-primary-300"
                        onClick={() => handleMatchingAnswer(item.id, item.id)}
                      >
                        {item.text}
                      </motion.div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Descriptions</h4>
                  <div className="space-y-2">
                    {section.activity.items.map((item: any) => (
                      <motion.div
                        key={`match-${item.id}`}
                        whileHover={{ scale: 1.02 }}
                        className={`p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                          matchingAnswers[item.id] === item.id
                            ? 'bg-green-100 border-green-300'
                            : 'bg-gray-100 border-gray-200 hover:border-primary-300'
                        }`}
                      >
                        {item.match}
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )
        }

        if (section.activity?.type === 'checklist') {
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-green-50 rounded-xl p-6"
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">{section.title}</h3>
              <p className="text-gray-600 mb-6">{section.content}</p>

              <div className="space-y-3">
                {section.activity.items.map((item: any) => (
                  <motion.div
                    key={item.id}
                    whileHover={{ scale: 1.01 }}
                    className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-green-300 transition-all duration-200"
                  >
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => playFeedback('click')}
                      className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
                        item.completed
                          ? 'bg-green-500 border-green-500 text-white'
                          : 'border-gray-300 hover:border-green-400'
                      }`}
                    >
                      {item.completed && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="text-xs"
                        >
                          ✓
                        </motion.div>
                      )}
                    </motion.button>
                    <span className={`font-medium ${item.completed ? 'text-green-700 line-through' : 'text-gray-900'}`}>
                      {item.text}
                    </span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )
        }
        break

      case 'quiz':
        return (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-purple-50 rounded-xl p-6"
          >
            <h3 className="text-xl font-bold text-gray-900 mb-6">{section.title}</h3>
            
            <div className="space-y-6">
              {section.questions.map((question: any, qIndex: number) => (
                <div key={qIndex} className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-4">{question.question}</h4>
                  <div className="space-y-2">
                    {question.options.map((option: string, oIndex: number) => (
                      <motion.button
                        key={oIndex}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleQuizAnswer(qIndex, oIndex)}
                        className={`w-full text-left p-3 rounded-lg border-2 transition-all duration-200 ${
                          quizAnswers[qIndex] === oIndex
                            ? oIndex === question.correct
                              ? 'bg-green-100 border-green-300 text-green-800'
                              : 'bg-red-100 border-red-300 text-red-800'
                            : 'bg-gray-50 border-gray-200 hover:border-primary-300'
                        }`}
                      >
                        {option}
                      </motion.button>
                    ))}
                  </div>
                  
                  {quizAnswers[qIndex] !== undefined && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`mt-3 p-3 rounded-lg ${
                        quizAnswers[qIndex] === question.correct
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {quizAnswers[qIndex] === question.correct
                        ? '✅ Correct! Well done!'
                        : `❌ Incorrect. The correct answer is: ${question.options[question.correct]}`
                      }
                      {question.explanation && (
                        <div className="mt-2 text-sm opacity-90">
                          💡 {question.explanation}
                        </div>
                      )}
                    </motion.div>
                  )}
                </div>
              ))}
            </div>
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href={`/modules/${moduleId}`}>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>Back to Module</span>
                </motion.button>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold text-gray-900">{content.title}</h1>
            </div>
            
            {isCompleted && (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span className="font-semibold">Completed</span>
              </div>
            )}
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Section {currentSection + 1} of {totalSections}</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <div className="progress-bar h-2">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
                className="h-full bg-gradient-to-r from-primary-400 to-primary-600 rounded-full"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSection}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-xl p-8 shadow-lg min-h-[500px]"
            >
              {renderSection(content.sections[currentSection], currentSection)}
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex items-center justify-between mt-8">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handlePrevious}
              disabled={currentSection === 0}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                currentSection === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Previous</span>
            </motion.button>

            <div className="flex items-center space-x-4">
              {/* Progress Dots */}
              <div className="flex items-center space-x-2">
                {Array.from({ length: totalSections }, (_, i) => (
                  <div
                    key={i}
                    className={`w-3 h-3 rounded-full transition-all duration-200 ${
                      i <= currentSection ? 'bg-primary-500' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              {/* Manual Save Button */}
              <motion.button
                whileHover={{ scale: isSaving ? 1 : 1.05 }}
                whileTap={{ scale: isSaving ? 1 : 0.95 }}
                onClick={handleManualSave}
                disabled={isSaving || isCompleting}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  isSaving || isCompleting
                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
                title="Save current progress"
              >
                {isSaving ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                    />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <span>Save Progress</span>
                  </>
                )}
              </motion.button>
            </div>

            <motion.button
              whileHover={{ scale: isCompleting ? 1 : 1.05 }}
              whileTap={{ scale: isCompleting ? 1 : 0.95 }}
              onClick={handleNext}
              disabled={isCompleting}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                isCompleting
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-primary-600 text-white hover:bg-primary-700'
              }`}
            >
              <span>
                {isCompleting
                  ? 'Saving Progress...'
                  : currentSection === totalSections - 1
                    ? isCompleted ? 'Chapter Completed' : 'Complete Chapter'
                    : 'Next'
                }
              </span>
              {isCompleting ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                />
              ) : currentSection === totalSections - 1 ? (
                <Trophy className="w-4 h-4" />
              ) : (
                <ArrowRight className="w-4 h-4" />
              )}
            </motion.button>
          </div>

          {/* Error Display */}
          {completionError && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg"
            >
              <div className="flex items-center space-x-2 text-red-700">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">{completionError}</span>
              </div>
              <button
                type="button"
                onClick={() => setCompletionError(null)}
                className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
              >
                Dismiss
              </button>
            </motion.div>
          )}

          {/* Completion Celebration */}
          <AnimatePresence>
            {isCompleted && currentSection === totalSections - 1 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5 }}
                className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
              >
                <motion.div
                  initial={{ y: 50 }}
                  animate={{ y: 0 }}
                  className="bg-white rounded-xl p-8 text-center max-w-md mx-4"
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4"
                  >
                    <Trophy className="w-8 h-8 text-white" />
                  </motion.div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Chapter Completed!</h2>
                  <p className="text-gray-600 mb-6">
                    Great job! You've earned 100 XP and unlocked the next chapter.
                  </p>
                  <div className="flex space-x-3">
                    <Link href={`/modules/${moduleId}`} className="flex-1">
                      <button type="button" className="w-full btn-primary">
                        Back to Module
                      </button>
                    </Link>
                    <button
                      type="button"
                      onClick={() => setIsCompleted(false)}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                      Close
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Success Notification */}
      <NotificationComponent />
    </div>
  )
}
