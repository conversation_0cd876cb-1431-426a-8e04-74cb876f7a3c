'use client'

import { useEffect, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface Particle {
  id: string
  x: number
  y: number
  vx: number
  vy: number
  life: number
  maxLife: number
  size: number
  color: string
  type: 'star' | 'circle' | 'heart' | 'sparkle' | 'coin' | 'confetti'
}

interface ParticleSystemProps {
  trigger: boolean
  type: 'celebration' | 'achievement' | 'levelUp' | 'coinCollect' | 'success'
  origin?: { x: number; y: number }
  count?: number
  duration?: number
  onComplete?: () => void
}

export function ParticleSystem({ 
  trigger, 
  type, 
  origin = { x: 50, y: 50 }, 
  count = 20,
  duration = 3000,
  onComplete 
}: ParticleSystemProps) {
  const [particles, setParticles] = useState<Particle[]>([])
  const animationRef = useRef<number>()
  const startTimeRef = useRef<number>()

  const particleConfigs = {
    celebration: {
      colors: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
      types: ['star', 'circle', 'heart', 'confetti'] as const,
      velocity: { min: 2, max: 8 },
      life: { min: 1000, max: 3000 },
      size: { min: 4, max: 12 }
    },
    achievement: {
      colors: ['#FFD700', '#FFA500', '#FF8C00'],
      types: ['star', 'sparkle'] as const,
      velocity: { min: 1, max: 5 },
      life: { min: 1500, max: 2500 },
      size: { min: 6, max: 16 }
    },
    levelUp: {
      colors: ['#9B59B6', '#8E44AD', '#E74C3C', '#F39C12'],
      types: ['star', 'circle', 'sparkle'] as const,
      velocity: { min: 3, max: 10 },
      life: { min: 2000, max: 4000 },
      size: { min: 8, max: 20 }
    },
    coinCollect: {
      colors: ['#FFD700', '#FFA500'],
      types: ['coin', 'sparkle'] as const,
      velocity: { min: 1, max: 4 },
      life: { min: 800, max: 1500 },
      size: { min: 6, max: 14 }
    },
    success: {
      colors: ['#2ECC71', '#27AE60', '#00D2FF'],
      types: ['circle', 'star'] as const,
      velocity: { min: 2, max: 6 },
      life: { min: 1000, max: 2000 },
      size: { min: 4, max: 10 }
    }
  }

  const createParticles = () => {
    const config = particleConfigs[type]
    const newParticles: Particle[] = []

    for (let i = 0; i < count; i++) {
      const angle = (Math.PI * 2 * i) / count + Math.random() * 0.5
      const velocity = config.velocity.min + Math.random() * (config.velocity.max - config.velocity.min)
      const life = config.life.min + Math.random() * (config.life.max - config.life.min)
      
      newParticles.push({
        id: `particle-${i}-${Date.now()}`,
        x: origin.x,
        y: origin.y,
        vx: Math.cos(angle) * velocity,
        vy: Math.sin(angle) * velocity,
        life,
        maxLife: life,
        size: config.size.min + Math.random() * (config.size.max - config.size.min),
        color: config.colors[Math.floor(Math.random() * config.colors.length)],
        type: config.types[Math.floor(Math.random() * config.types.length)]
      })
    }

    setParticles(newParticles)
    startTimeRef.current = Date.now()
  }

  const updateParticles = () => {
    const now = Date.now()
    const elapsed = now - (startTimeRef.current || now)

    if (elapsed > duration) {
      setParticles([])
      onComplete?.()
      return
    }

    setParticles(prev => prev
      .map(particle => ({
        ...particle,
        x: particle.x + particle.vx,
        y: particle.y + particle.vy,
        vy: particle.vy + 0.1, // gravity
        life: particle.life - 16 // assuming 60fps
      }))
      .filter(particle => particle.life > 0 && particle.y < 100)
    )

    animationRef.current = requestAnimationFrame(updateParticles)
  }

  useEffect(() => {
    if (trigger) {
      createParticles()
      animationRef.current = requestAnimationFrame(updateParticles)
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [trigger])

  const renderParticle = (particle: Particle) => {
    const opacity = particle.life / particle.maxLife
    const scale = Math.min(1, particle.life / (particle.maxLife * 0.2))

    const particleElements = {
      star: (
        <div
          className="absolute pointer-events-none"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            transform: 'translate(-50%, -50%)',
            opacity,
            color: particle.color,
            fontSize: `${particle.size}px`,
            filter: 'drop-shadow(0 0 4px currentColor)'
          }}
        >
          ⭐
        </div>
      ),
      circle: (
        <div
          className="absolute pointer-events-none rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            transform: `translate(-50%, -50%) scale(${scale})`,
            opacity,
            boxShadow: `0 0 ${particle.size}px ${particle.color}40`
          }}
        />
      ),
      heart: (
        <div
          className="absolute pointer-events-none"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            transform: `translate(-50%, -50%) scale(${scale})`,
            opacity,
            color: particle.color,
            fontSize: `${particle.size}px`,
            filter: 'drop-shadow(0 0 4px currentColor)'
          }}
        >
          ❤️
        </div>
      ),
      sparkle: (
        <div
          className="absolute pointer-events-none"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            transform: `translate(-50%, -50%) scale(${scale}) rotate(${particle.life * 0.5}deg)`,
            opacity,
            color: particle.color,
            fontSize: `${particle.size}px`,
            filter: 'drop-shadow(0 0 6px currentColor)'
          }}
        >
          ✨
        </div>
      ),
      coin: (
        <div
          className="absolute pointer-events-none"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            transform: `translate(-50%, -50%) scale(${scale})`,
            opacity,
            color: particle.color,
            fontSize: `${particle.size}px`,
            filter: 'drop-shadow(0 0 4px currentColor)'
          }}
        >
          🪙
        </div>
      ),
      confetti: (
        <div
          className="absolute pointer-events-none"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size * 0.6}px`,
            backgroundColor: particle.color,
            transform: `translate(-50%, -50%) scale(${scale}) rotate(${particle.life * 2}deg)`,
            opacity,
            borderRadius: '2px'
          }}
        />
      )
    }

    return particleElements[particle.type]
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-50 overflow-hidden">
      <AnimatePresence>
        {particles.map(particle => (
          <motion.div
            key={particle.id}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {renderParticle(particle)}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}

// Celebration trigger component
export function CelebrationTrigger({ 
  children, 
  type = 'success',
  onTrigger 
}: { 
  children: React.ReactNode
  type?: 'celebration' | 'achievement' | 'levelUp' | 'coinCollect' | 'success'
  onTrigger?: () => void 
}) {
  const [showParticles, setShowParticles] = useState(false)
  const [origin, setOrigin] = useState({ x: 50, y: 50 })

  const handleClick = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 100
    const y = ((event.clientY - rect.top) / rect.height) * 100
    
    setOrigin({ x, y })
    setShowParticles(true)
    onTrigger?.()
    
    setTimeout(() => setShowParticles(false), 100)
  }

  return (
    <div className="relative" onClick={handleClick}>
      {children}
      <ParticleSystem
        trigger={showParticles}
        type={type}
        origin={origin}
        count={type === 'levelUp' ? 30 : 15}
        onComplete={() => setShowParticles(false)}
      />
    </div>
  )
}
