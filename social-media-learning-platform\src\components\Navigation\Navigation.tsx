'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Home, 
  BookOpen, 
  Trophy, 
  BarChart3, 
  Menu, 
  X, 
  Star,
  Zap
} from 'lucide-react'
import { useGame } from '@/contexts/GameContext'
import { useAuth } from '@/contexts/AuthContext'
import { useAudio } from '@/hooks/useAudio'
import { AuthModal } from '@/components/Auth/AuthModal'

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const pathname = usePathname()
  const { gameState } = useGame()
  const { authState, signOut } = useAuth()
  const { playFeedback } = useAudio()

  const navItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/modules', label: 'Modules', icon: BookO<PERSON> },
    { href: '/dashboard', label: 'Dashboard', icon: BarChart3 },
    { href: '/achievements', label: 'Achievements', icon: Trophy },
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <nav className="bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
              className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center"
            >
              <Zap className="w-5 h-5 text-white" />
            </motion.div>
            <span className="text-lg sm:text-xl font-bold gradient-text hidden xs:block">
              Social Media Quest
            </span>
            <span className="text-lg font-bold gradient-text block xs:hidden">
              SMQ
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => playFeedback('click')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  isActive(item.href)
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:text-primary-600 hover:bg-gray-100'
                }`}
              >
                <item.icon className="w-4 h-4" />
                <span className="font-medium">{item.label}</span>
              </Link>
            ))}
          </div>

          {/* User Stats - Only show when authenticated */}
          {authState.isAuthenticated && (
            <div className="hidden md:flex items-center space-x-4">
              <div className="flex items-center space-x-2 px-3 py-1 bg-gradient-to-r from-yellow-100 to-orange-100 rounded-full">
                <Star className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-semibold text-yellow-800">
                  {gameState.totalXP} XP
                </span>
              </div>
              <div className="flex items-center space-x-2 px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full">
                <Trophy className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-semibold text-purple-800">
                  Level {gameState.level}
                </span>
              </div>
            </div>
          )}

          {/* Authentication */}
          <div className="hidden md:flex items-center space-x-2">
            {authState.isAuthenticated ? (
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-600">
                  Welcome, {authState.user?.full_name || authState.user?.email}
                </span>
                <button
                  type="button"
                  onClick={() => {
                    signOut()
                    playFeedback('click')
                  }}
                  className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <button
                type="button"
                onClick={() => {
                  setAuthModalOpen(true)
                  playFeedback('click')
                }}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
              >
                Sign In
              </button>
            )}
          </div>

          {/* Mobile Actions */}
          <div className="md:hidden flex items-center space-x-2">
            {/* Mobile Auth Button */}
            {!authState.isAuthenticated && (
              <button
                type="button"
                onClick={() => {
                  setAuthModalOpen(true)
                  playFeedback('click')
                }}
                className="px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium text-sm min-h-[44px]"
              >
                Sign In
              </button>
            )}

            {/* Mobile Menu Button */}
            <button
              type="button"
              onClick={() => {
                setIsOpen(!isOpen)
                playFeedback('click')
              }}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors min-w-[44px] min-h-[44px] flex items-center justify-center"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="md:hidden border-t border-gray-200 py-4"
            >
              <div className="flex flex-col space-y-2">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={() => setIsOpen(false)}
                    className={`flex items-center space-x-3 px-4 py-4 rounded-lg transition-all duration-200 min-h-[44px] ${
                      isActive(item.href)
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-primary-600 hover:bg-gray-100'
                    }`}
                  >
                    <item.icon className="w-5 h-5" />
                    <span className="font-medium text-base">{item.label}</span>
                  </Link>
                ))}
                
                {/* Mobile Authentication */}
                {authState.isAuthenticated ? (
                  <div className="px-4 py-3 mt-4 border-t border-gray-200 space-y-3">
                    <div className="text-sm text-gray-600">
                      Welcome, {authState.user?.full_name || authState.user?.email}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        signOut()
                        setIsOpen(false)
                        playFeedback('click')
                      }}
                      className="w-full px-4 py-3 text-left bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors min-h-[44px] text-base"
                    >
                      Sign Out
                    </button>
                  </div>
                ) : (
                  <div className="px-4 py-3 mt-4 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={() => {
                        setAuthModalOpen(true)
                        setIsOpen(false)
                        playFeedback('click')
                      }}
                      className="w-full px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium min-h-[44px] text-base"
                    >
                      Sign In
                    </button>
                  </div>
                )}

                {/* Mobile User Stats - Only show when authenticated */}
                {authState.isAuthenticated && (
                  <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200">
                    <div className="flex items-center space-x-2">
                      <Star className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm font-semibold text-gray-700">
                        {gameState.totalXP} XP
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Trophy className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-semibold text-gray-700">
                        Level {gameState.level}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
      />
    </nav>
  )
}
