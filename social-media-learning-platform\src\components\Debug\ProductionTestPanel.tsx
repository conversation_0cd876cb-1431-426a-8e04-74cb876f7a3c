'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Bug, Play, CheckCircle, XCircle, Clock, ChevronDown, ChevronUp } from 'lucide-react'

interface TestResult {
  name: string
  passed: boolean
  message: string
  details?: any
}

interface TestSuite {
  name: string
  tests: TestResult[]
  passed: boolean
  duration: number
}

export function ProductionTestPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<TestSuite[]>([])
  const [expandedSuites, setExpandedSuites] = useState<Set<string>>(new Set())

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const runTests = async () => {
    setIsRunning(true)
    setResults([])
    
    try {
      const { productionTester } = await import('@/utils/testUtils')
      const testResults = await productionTester.runAllTests()
      setResults(testResults)
      
      // Auto-expand failed suites
      const failedSuites = new Set(
        testResults.filter(suite => !suite.passed).map(suite => suite.name)
      )
      setExpandedSuites(failedSuites)
      
      // Log report to console
      const report = productionTester.generateReport()
      console.log(report)
    } catch (error) {
      console.error('Failed to run tests:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const toggleSuite = (suiteName: string) => {
    const newExpanded = new Set(expandedSuites)
    if (newExpanded.has(suiteName)) {
      newExpanded.delete(suiteName)
    } else {
      newExpanded.add(suiteName)
    }
    setExpandedSuites(newExpanded)
  }

  const totalTests = results.reduce((sum, suite) => sum + suite.tests.length, 0)
  const passedTests = results.reduce((sum, suite) => 
    sum + suite.tests.filter(test => test.passed).length, 0
  )

  return (
    <div className="fixed bottom-20 right-6 z-40">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            className="absolute bottom-16 right-0 w-96 max-h-96 bg-white rounded-xl shadow-2xl border overflow-hidden"
          >
            <div className="bg-gray-50 px-4 py-3 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Bug className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold text-gray-900">Production Tests</h3>
                </div>
                {results.length > 0 && (
                  <div className="text-sm text-gray-600">
                    {passedTests}/{totalTests} passed
                  </div>
                )}
              </div>
            </div>

            <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={runTests}
                disabled={isRunning}
                className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-colors ${
                  isRunning 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                {isRunning ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                    />
                    <span>Running Tests...</span>
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    <span>Run All Tests</span>
                  </>
                )}
              </motion.button>

              {results.length > 0 && (
                <div className="space-y-2">
                  {results.map((suite) => (
                    <div key={suite.name} className="border rounded-lg overflow-hidden">
                      <button
                        onClick={() => toggleSuite(suite.name)}
                        className={`w-full flex items-center justify-between p-3 text-left transition-colors ${
                          suite.passed ? 'bg-green-50 hover:bg-green-100' : 'bg-red-50 hover:bg-red-100'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          {suite.passed ? (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-600" />
                          )}
                          <span className="font-medium text-sm">{suite.name}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{suite.duration}ms</span>
                          </div>
                          {expandedSuites.has(suite.name) ? (
                            <ChevronUp className="w-4 h-4 text-gray-400" />
                          ) : (
                            <ChevronDown className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                      </button>

                      <AnimatePresence>
                        {expandedSuites.has(suite.name) && (
                          <motion.div
                            initial={{ height: 0 }}
                            animate={{ height: 'auto' }}
                            exit={{ height: 0 }}
                            className="overflow-hidden"
                          >
                            <div className="p-3 bg-gray-50 border-t space-y-2">
                              {suite.tests.map((test, index) => (
                                <div key={index} className="flex items-start space-x-2">
                                  {test.passed ? (
                                    <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                                  ) : (
                                    <XCircle className="w-3 h-3 text-red-600 mt-0.5 flex-shrink-0" />
                                  )}
                                  <div className="flex-1 min-w-0">
                                    <div className="text-xs font-medium text-gray-900">
                                      {test.name}
                                    </div>
                                    <div className="text-xs text-gray-600 break-words">
                                      {test.message}
                                    </div>
                                    {test.details && (
                                      <div className="text-xs text-gray-500 mt-1">
                                        {JSON.stringify(test.details, null, 2)}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ))}
                </div>
              )}

              {results.length > 0 && (
                <div className="text-xs text-gray-500 text-center pt-2 border-t">
                  Check console for detailed report
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsOpen(!isOpen)}
        className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
        title="Production Tests"
      >
        <Bug className="w-5 h-5" />
      </motion.button>
    </div>
  )
}
