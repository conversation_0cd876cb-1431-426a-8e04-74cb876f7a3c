'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Lock, Star, Trophy, Zap, BookOpen, Target } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { AuthModal } from './AuthModal'
import { useAudio } from '@/hooks/useAudio'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  showPreview?: boolean
}

export function AuthGuard({ children, requireAuth = true, showPreview = false }: AuthGuardProps) {
  const { authState } = useAuth()
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [hasTriedAccess, setHasTriedAccess] = useState(false)
  const { playFeedback } = useAudio()
  const router = useRouter()
  const pathname = usePathname()

  // Public routes that don't require authentication
  const publicRoutes = ['/', '/about', '/pricing']
  const isPublicRoute = publicRoutes.includes(pathname)

  useEffect(() => {
    if (requireAuth && !authState.isAuthenticated && !isPublicRoute) {
      setHasTriedAccess(true)
      playFeedback('error')
    }
  }, [requireAuth, authState.isAuthenticated, isPublicRoute, playFeedback])

  // If authentication is required but user is not authenticated
  if (requireAuth && !authState.isAuthenticated && !isPublicRoute) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.5, type: "spring" }}
            className="flex items-center justify-center min-h-screen p-4"
          >
            <div className="max-w-md w-full">
              {/* Premium Lock Animation */}
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                className="text-center mb-8"
              >
                <motion.div
                  animate={{ 
                    rotate: [0, -10, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3
                  }}
                  className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-2xl"
                >
                  <Lock className="w-10 h-10 text-white" />
                </motion.div>
                
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                  className="text-3xl font-bold text-gray-900 mb-4"
                >
                  Premium Content Awaits! 🚀
                </motion.h1>
                
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                  className="text-gray-600 text-lg leading-relaxed"
                >
                  Join thousands of learners mastering social media marketing through our gamified platform.
                </motion.p>
              </motion.div>

              {/* Feature Highlights */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.6 }}
                className="grid grid-cols-2 gap-4 mb-8"
              >
                {[
                  { icon: Star, label: 'Earn XP', color: 'from-yellow-400 to-orange-500' },
                  { icon: Trophy, label: 'Achievements', color: 'from-purple-400 to-pink-500' },
                  { icon: Target, label: 'Track Progress', color: 'from-blue-400 to-cyan-500' },
                  { icon: BookOpen, label: 'Expert Content', color: 'from-green-400 to-emerald-500' }
                ].map((feature, index) => (
                  <motion.div
                    key={feature.label}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1 + index * 0.1, duration: 0.4 }}
                    className="text-center p-4 bg-white rounded-xl shadow-lg border border-gray-100"
                  >
                    <div className={`w-12 h-12 mx-auto mb-2 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-sm font-medium text-gray-700">{feature.label}</span>
                  </motion.div>
                ))}
              </motion.div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.4, duration: 0.6 }}
                className="space-y-3"
              >
                <motion.button
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    setShowAuthModal(true)
                    playFeedback('click')
                  }}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 min-h-[44px]"
                >
                  🚀 Start Learning Now
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    router.push('/')
                    playFeedback('click')
                  }}
                  className="w-full bg-white text-gray-700 py-3 px-6 rounded-xl font-medium border border-gray-200 hover:bg-gray-50 transition-all duration-300 min-h-[44px]"
                >
                  ← Back to Home
                </motion.button>
              </motion.div>

              {/* Floating Particles */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-60"
                    animate={{
                      x: [0, 100, 0],
                      y: [0, -100, 0],
                      scale: [1, 1.5, 1],
                      opacity: [0.6, 0.2, 0.6]
                    }}
                    transition={{
                      duration: 4 + i,
                      repeat: Infinity,
                      delay: i * 0.5
                    }}
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${30 + i * 10}%`
                    }}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Auth Modal */}
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          initialMode="signup"
        />
      </div>
    )
  }

  // If showing preview mode (for module cards, etc.)
  if (showPreview && !authState.isAuthenticated) {
    return (
      <div className="relative">
        {children}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent flex items-end justify-center p-6">
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              setShowAuthModal(true)
              playFeedback('click')
            }}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold shadow-lg flex items-center space-x-2 min-h-[44px]"
          >
            <Lock className="w-4 h-4" />
            <span>Unlock Premium Content</span>
          </motion.button>
        </div>
        
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          initialMode="signup"
        />
      </div>
    )
  }

  // User is authenticated or route doesn't require auth
  return <>{children}</>
}
