# Audio Files Setup

## Background Music

The platform requires a background music file for the premium audio experience. 

### Required File
- **Filename**: `Chill Lofi Mix [chill lo-fi hip hop beats].mp3`
- **Location**: `public/audio/`
- **Format**: MP3
- **Recommended**: Lofi hip-hop or chill background music
- **Duration**: 3+ minutes (will loop automatically)
- **Volume**: The app will control volume automatically

### Setup Instructions

1. **Option 1: Use your own audio file**
   - Place your background music file in `public/audio/`
   - Rename it to: `Chill Lofi Mix [chill lo-fi hip hop beats].mp3`
   - Ensure it's in MP3 format

2. **Option 2: Use a placeholder**
   - The app will gracefully handle missing audio files
   - Users will see audio controls but music won't play
   - No errors will be thrown

### Audio Features

- **Auto-play**: Music starts automatically on first user interaction (respects browser policies)
- **Loop**: Background music loops continuously
- **Volume Control**: Users can adjust volume via audio settings
- **Mute/Unmute**: Users can toggle music on/off
- **Cross-page**: Music continues playing across page navigation
- **Mobile Friendly**: Works on mobile devices with proper user interaction

### Technical Notes

- Audio files are excluded from git to avoid large file issues
- The app uses HTML5 Audio API for playback
- Fallback handling for browsers that don't support autoplay
- Error handling for missing or corrupted audio files

### File Size Considerations

- Keep audio files under 50MB for better loading performance
- Consider using compressed MP3 files (128kbps is usually sufficient)
- For production, consider hosting audio files on a CDN
